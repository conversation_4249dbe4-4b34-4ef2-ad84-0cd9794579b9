import React from 'react';
import imgBrain11 from '../../assets/figma/aecd548cefa05584ea3adc21511901f481922d21.png';
import imgLine50 from '../../assets/figma/bcb3cc7c3f9a0f83024bcced155d4e8276b6cbda.svg';

export const NewHeader: React.FC = () => {
  return (
    <header className="absolute w-full top-0 left-0 z-10">
      <div className="relative max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between h-28">
        <div className="flex items-center">
          <img src={imgBrain11} alt="EthosPrompt Logo" className="h-9 w-9 mr-2" />
          <span className="text-3xl font-bold text-[#030823]">EthosPrompt</span>
        </div>
        <nav className="flex items-center space-x-12">
          <a href="#" className="text-2xl font-bold text-[#030823] relative">
            Home
            <img src={imgLine50} alt="" className="absolute -bottom-2 left-0 w-full h-1" />
          </a>
          <a href="#" className="text-2xl font-bold text-[#030823]">Prompt Library</a>
          <a href="#" className="text-2xl font-bold text-[#030823]">Prompting Guide</a>
          <a href="#" className="text-2xl font-bold text-[#030823]">Contact us</a>
        </nav>
        <a href="#" className="px-8 py-4 bg-gradient-to-b from-[#D47CD9] to-[#7409C5] text-white font-semibold rounded-lg shadow-lg">
          Get Started
        </a>
      </div>
    </header>
  );
};
