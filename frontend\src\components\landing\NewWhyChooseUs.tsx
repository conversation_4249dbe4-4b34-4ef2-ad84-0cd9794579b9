import React from 'react';
import imgImage25 from '../../assets/figma/ac8560907285b10661a9de9c3f7f9c60db71090a.png';
import imgImage26 from '../../assets/figma/e47d38d67ca45b017f89a9bf9d00f125ce1ceae6.png';
import imgImage27 from '../../assets/figma/ec64f6b61587e04b374397f852916b0abdfc6c3a.png';

export const NewWhyChooseUs: React.FC = () => {
  return (
    <section className="py-32 bg-white">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-6xl font-bold text-[#030823]">Why Choose Us?</h2>
        <p className="mt-6 max-w-3xl mx-auto text-2xl text-[#484848] leading-relaxed">
          We provide the most advanced AI agents to help you automate your business and provide the best customer experience.
        </p>
        <div className="mt-20 grid md:grid-cols-3 gap-16">
          <div className="flex flex-col items-center">
            <img className="h-48 w-48" src={imgImage25} alt="Advanced AI Technology" />
            <h3 className="mt-8 text-4xl font-bold text-[#030823]">Advanced AI Technology</h3>
            <p className="mt-4 text-xl text-[#484848] leading-relaxed">
              Our agents are powered by the latest AI models, ensuring high accuracy and human-like conversations.
            </p>
          </div>
          <div className="flex flex-col items-center">
            <img className="h-48 w-48" src={imgImage26} alt="Easy Integration" />
            <h3 className="mt-8 text-4xl font-bold text-[#030823]">Easy Integration</h3>
            <p className="mt-4 text-xl text-[#484848] leading-relaxed">
              Seamlessly integrate with your existing tools and platforms with just a few clicks.
            </p>
          </div>
          <div className="flex flex-col items-center">
            <img className="h-48 w-48" src={imgImage27} alt="24/7 Support" />
            <h3 className="mt-8 text-4xl font-bold text-[#030823]">24/7 Support</h3>
            <p className="mt-4 text-xl text-[#484848] leading-relaxed">
              Our team is always here to help you with any questions or issues you may have.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};
