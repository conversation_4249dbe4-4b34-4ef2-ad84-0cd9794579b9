# 🎨 Figma MCP Quick Reference Card

## ✅ Connection Status: WORKING
- **Server**: `http://127.0.0.1:3845/sse` ✅
- **VS Code**: Configured ✅
- **Ready to use**: Yes ✅

---

## 🚀 Quick Commands for Landing Page Development

### 1. Generate Code from Selected Layer
```
Generate React TypeScript code for my current Figma selection using Tailwind CSS
```

### 2. Update Existing Landing Page Component
```
Update [ComponentName].tsx to match this Figma design selection
```
**Examples:**
- `Update HeroSection.tsx to match this Figma design`
- `Update NavigationHeader.tsx to match this Figma design`
- `Update FeaturesSection.tsx to match this Figma design`

### 3. Extract Design Tokens
```
Get the color palette, typography, and spacing variables from my Figma selection
```

### 4. Create New Landing Page Section
```
Create a new landing page section component from this Figma selection
```

### 5. Analyze Layout and Spacing
```
Analyze the layout, spacing, and responsive behavior of this Figma selection
```

---

## 🎯 Landing Page Component Mapping

| Select in Figma | Generate/Update Component |
|----------------|---------------------------|
| Hero Section | `HeroSection.tsx` |
| Navigation | `NavigationHeader.tsx` |
| Features | `FeaturesSection.tsx` |
| Benefits | `BenefitsSection.tsx` |
| Testimonials | `TestimonialsSection.tsx` |
| CTA | `CTASection.tsx` |
| Footer | `FooterSection.tsx` |

---

## 📋 Step-by-Step Workflow

1. **Open Figma Desktop App** ✅
2. **Open your landing page design file**
3. **Select a frame/component** in Figma
4. **Use one of the commands above** in VS Code
5. **Review and integrate** the generated code

---

## 🔧 Troubleshooting

**If MCP tools aren't available:**
1. Ensure Figma desktop app is open
2. Verify Dev Mode MCP Server is enabled in Figma preferences
3. Restart VS Code
4. Check that you have Professional/Organization/Enterprise plan

**If connection fails:**
- Restart Figma desktop app
- Verify server URL: `http://127.0.0.1:3845/sse`

---

## 💡 Pro Tips

- **Use smaller selections** for better code generation
- **Name layers semantically** in Figma (e.g., "HeroSection" not "Group 1")
- **Enable Auto Layout** in Figma for responsive behavior
- **Use Figma variables** for consistent design tokens
- **Break down large frames** into smaller components

---

**Ready to start?** Select a layer in Figma and try one of the commands above! 🎨 