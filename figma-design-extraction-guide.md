# 🎨 Figma Design Extraction & Implementation Guide

## 🎯 Complete Landing Page Design Implementation

This guide will help you extract your landing page design from Figma and implement it as pixel-perfect React components.

---

## 📋 Step-by-Step Extraction Process

### Step 1: Connect to Figma MCP

1. **Open Figma Desktop App** ✅ (Already running)
2. **Open your landing page design file**
3. **Select the main landing page frame/artboard**

### Step 2: Extract Design Information

Use these Figma MCP commands in VS Code:

#### A. Get Complete Page Structure
```
Analyze the complete landing page design structure from my Figma selection. 
Extract all sections, components, layout, spacing, and visual hierarchy.
```

#### B. Extract Design Tokens
```
Extract all design tokens from my Figma selection including:
- Color palette (hex values)
- Typography (font families, sizes, weights)
- Spacing (margins, padding, gaps)
- Border radius values
- Shadow styles
- Breakpoints for responsive design
```

#### C. Extract Visual Assets
```
List all visual assets in my Figma selection including:
- Images and illustrations
- Icons and graphics
- Background patterns
- Logo and branding elements
- Any interactive elements or animations
```

#### D. Generate Component Code
```
Generate React TypeScript components for my Figma selection using Tailwind CSS.
Create separate components for each major section and ensure pixel-perfect accuracy.
Include all necessary props, responsive behavior, and interactive elements.
```

---

## 🏗️ Component Implementation Strategy

### 1. Landing Page Structure

Based on your existing components, implement these sections:

```typescript
// Main Landing Page Component
<LandingPage>
  <NavigationHeader />     // Fixed header with logo and nav
  <HeroSection />          // Main hero with CTA
  <FeaturesSection />      // Feature highlights
  <BenefitsSection />      // Value propositions
  <TestimonialsSection />  // Customer testimonials
  <CTASection />          // Call-to-action
  <FooterSection />       // Footer with links
</LandingPage>
```

### 2. Asset Organization

Create this folder structure for assets:

```
frontend/public/assets/landing/
├── images/
│   ├── hero-background.jpg
│   ├── hero-illustration.svg
│   ├── feature-icons/
│   │   ├── icon-1.svg
│   │   ├── icon-2.svg
│   │   └── icon-3.svg
│   └── testimonials/
│       ├── avatar-1.jpg
│       ├── avatar-2.jpg
│       └── avatar-3.jpg
├── icons/
│   ├── logo.svg
│   └── favicon.ico
└── patterns/
    └── background-pattern.svg
```

### 3. Design Token Implementation

Create a design tokens file:

```typescript
// frontend/src/styles/design-tokens.ts
export const designTokens = {
  colors: {
    primary: {
      50: '#eef2ff',
      500: '#6366f1',
      600: '#4f46e5',
      700: '#4338ca',
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      900: '#111827',
    }
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
      '7xl': '4.5rem',
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
  }
};
```

---

## 🎨 Component Implementation Examples

### 1. Updated Hero Section

```typescript
// frontend/src/components/landing/HeroSection.tsx
import React from 'react';
import { ArrowRight, Sparkles, Play } from 'lucide-react';

export const HeroSection: React.FC = () => {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div className="text-center">
          {/* Badge */}
          <div className="flex justify-center mb-8">
            <div className="flex items-center space-x-2 bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full text-sm font-medium">
              <Sparkles className="h-4 w-4" />
              <span>AI-Powered Prompt Library</span>
            </div>
          </div>

          {/* Main headline */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-8 leading-tight">
            Build Smarter AI,{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
              Faster
            </span>
          </h1>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
            Your central hub for dynamic, data-aware AI prompts and automated workflows. 
            Transform static prompts into intelligent, context-aware AI systems.
          </p>

          {/* CTA buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <button className="inline-flex items-center px-8 py-4 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors duration-200 shadow-lg">
              Get Started Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </button>
            <button className="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors duration-200">
              <Play className="mr-2 h-5 w-5" />
              Watch Demo
            </button>
          </div>

          {/* Hero image */}
          <div className="relative max-w-5xl mx-auto">
            <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 p-8">
              <img 
                src="/assets/landing/images/hero-illustration.svg" 
                alt="AI Prompt Library Dashboard"
                className="w-full h-auto rounded-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
```

### 2. Enhanced Navigation Header

```typescript
// frontend/src/components/landing/NavigationHeader.tsx
import React, { useState } from 'react';
import { Menu, X, Brain } from 'lucide-react';

export const NavigationHeader: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-gray-200 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <img src="/assets/landing/icons/logo.svg" alt="PromptLibrary" className="h-8 w-8" />
            <span className="text-xl font-bold text-gray-900">PromptLibrary</span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">Features</a>
            <a href="#benefits" className="text-gray-600 hover:text-gray-900 transition-colors">Benefits</a>
            <a href="#testimonials" className="text-gray-600 hover:text-gray-900 transition-colors">Testimonials</a>
            <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">Pricing</a>
          </nav>

          {/* CTA Button */}
          <div className="hidden md:flex items-center space-x-4">
            <button className="text-gray-600 hover:text-gray-900 transition-colors">Sign In</button>
            <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
              Get Started
            </button>
          </div>

          {/* Mobile menu button */}
          <button 
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">Features</a>
              <a href="#benefits" className="text-gray-600 hover:text-gray-900 transition-colors">Benefits</a>
              <a href="#testimonials" className="text-gray-600 hover:text-gray-900 transition-colors">Testimonials</a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">Pricing</a>
              <div className="pt-4 border-t border-gray-200">
                <button className="w-full text-left text-gray-600 hover:text-gray-900 transition-colors mb-2">Sign In</button>
                <button className="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                  Get Started
                </button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};
```

---

## 🚀 Implementation Commands

### Use these commands in VS Code with Figma MCP:

1. **Extract Complete Design:**
```
Generate a complete React TypeScript landing page implementation from my Figma selection. 
Include all sections, components, styling, and assets. Make it pixel-perfect and production-ready.
```

2. **Update Specific Component:**
```
Update the [ComponentName].tsx component to match this Figma design exactly. 
Use our existing component structure and ensure all styling matches the design perfectly.
```

3. **Extract Assets:**
```
Extract all images, icons, and graphics from my Figma selection and provide the file paths 
and implementation code for using them in our React components.
```

4. **Get Design Tokens:**
```
Extract all design tokens from my Figma selection and create a comprehensive design system 
with colors, typography, spacing, and other visual properties for our landing page.
```

5. **Generate Responsive Code:**
```
Generate responsive React components from my Figma selection that work perfectly on 
mobile, tablet, and desktop. Include all breakpoints and responsive behavior.
```

---

## 📁 File Structure

After extraction, your landing page should have this structure:

```
frontend/src/components/landing/
├── LandingPage.tsx           # Main landing page component
├── NavigationHeader.tsx      # Navigation with logo and menu
├── HeroSection.tsx          # Hero section with CTA
├── FeaturesSection.tsx      # Feature highlights
├── BenefitsSection.tsx      # Value propositions
├── TestimonialsSection.tsx  # Customer testimonials
├── CTASection.tsx          # Call-to-action section
└── FooterSection.tsx       # Footer with links

frontend/public/assets/landing/
├── images/
│   ├── hero-background.jpg
│   ├── hero-illustration.svg
│   ├── feature-icons/
│   └── testimonials/
├── icons/
│   ├── logo.svg
│   └── favicon.ico
└── patterns/
    └── background-pattern.svg

frontend/src/styles/
└── design-tokens.ts         # Design system tokens
```

---

## 🎯 Next Steps

1. **Open your Figma design file**
2. **Select the landing page frame/artboard**
3. **Use the extraction commands above in VS Code**
4. **Review and refine the generated components**
5. **Test the implementation across different devices**
6. **Optimize performance and accessibility**

---

## 🔧 Troubleshooting

**If Figma MCP isn't working:**
- Ensure Figma desktop app is open
- Verify Dev Mode MCP Server is enabled
- Restart VS Code after configuration changes
- Check that you have Professional/Organization/Enterprise plan

**If generated code doesn't match design:**
- Use smaller, more specific selections
- Ensure Figma components are properly structured
- Use semantic layer names in Figma
- Break down large frames into smaller sections

---

**Ready to extract your design?** Open Figma, select your landing page, and use the commands above! 🎨 