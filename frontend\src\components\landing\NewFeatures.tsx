import React from 'react';
import imgPortraitMiddleAgedBusinesswoman1 from '../../assets/figma/3806640888f4b86f1967fd9430c374d3ff9b1d15.png';
import imgCloseupYoungFemaleProfessionalMakingEyeContactAgainstColoredBackground1 from '../../assets/figma/79a0265a4214e279cd3b71d3fb2f8dcd1e93e768.png';
import imgYoungBeardedManWithStripedShirt1 from '../../assets/figma/cc1d39e1afc978e28f8ca4f3b48ccb29b63fdb25.png';

const FeatureCard: React.FC<{ imgSrc: string; title: string; description: string; reverse?: boolean }> = ({ imgSrc, title, description, reverse = false }) => (
  <div className={`flex flex-col md:flex-row items-center gap-16 ${reverse ? 'md:flex-row-reverse' : ''}`}>
    <div className="md:w-1/2">
      <img src={imgSrc} alt={title} className="rounded-xl shadow-2xl" />
    </div>
    <div className="md:w-1/2">
      <h3 className="text-5xl font-bold text-[#030823]">{title}</h3>
      <p className="mt-6 text-xl text-[#484848] leading-relaxed">{description}</p>
      <a href="#" className="mt-8 inline-block text-2xl font-semibold text-[#7409C5] hover:underline">
        Learn More →
      </a>
    </div>
  </div>
);

export const NewFeatures: React.FC = () => {
  const features = [
    {
      imgSrc: imgPortraitMiddleAgedBusinesswoman1,
      title: 'Automated Customer Support',
      description: 'Provide instant, 24/7 support to your customers. Our AI agents can answer questions, resolve issues, and escalate to human agents when necessary, all based on your provided knowledge base.',
    },
    {
      imgSrc: imgCloseupYoungFemaleProfessionalMakingEyeContactAgainstColoredBackground1,
      title: 'Intelligent Sales Assistant',
      description: 'Qualify leads, book meetings, and answer product questions in real-time. Free up your sales team to focus on closing deals while the AI handles the initial engagement.',
      reverse: true,
    },
    {
      imgSrc: imgYoungBeardedManWithStripedShirt1,
      title: 'Internal Workflow Automation',
      description: 'Streamline internal processes by creating AI agents for HR, IT, and other departments. Get instant answers to common questions and automate repetitive tasks.',
    },
  ];

  return (
    <section className="py-32 bg-white">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-24">
          <h2 className="text-6xl font-bold text-[#030823]">
            Use Cases for Every Team
          </h2>
        </div>
        <div className="space-y-28">
          {features.map((feature, index) => (
            <FeatureCard key={index} {...feature} />
          ))}
        </div>
      </div>
    </section>
  );
};
