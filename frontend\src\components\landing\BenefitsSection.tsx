import React from 'react';
import { CheckCircle } from 'lucide-react';

export const BenefitsSection: React.FC = () => {
  const benefits = [
    'Reduce prompt development time by 80%',
    'Improve AI response accuracy by 60%',
    'Scale from 1 to 1000+ prompts seamlessly',
    'Integrate with any AI model or API',
    'Real-time collaboration and version control',
    'Advanced analytics and performance tracking'
  ];

  return (
    <section id="benefits" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Why Choose PromptLibrary?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Join thousands of teams who have transformed their AI workflows with our platform.
            </p>
            
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{benefit}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8">
            <img 
              src="/assets/landing/images/hero-illustration.svg" 
              alt="Benefits Illustration"
              className="w-full h-auto rounded-lg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};
