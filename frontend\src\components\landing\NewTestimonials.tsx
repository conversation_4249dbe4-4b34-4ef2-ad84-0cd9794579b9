import React from 'react';
import img816312 from '../../assets/figma/4f61ddb6debb5a8b6682079847ce4f0ee077d26a.png';
import imgCloseUpShotSmilingGladWomanWithShortHairstyleWearsRedRimedSpectaclesDressedCasuallyIsolatedWhiteWallExpressesPositiveFeelingsPeopleBeautyConcept1 from '../../assets/figma/96d245ea65a7fa752b2f53e2f411cd81f3453d59.png';
import imgStar1 from '../../assets/figma/b68c32a820ffe2185b1f79c12b1ae2509373591f.svg';

const TestimonialCard: React.FC<{ quote: string; name: string; title: string; imgSrc: string; }> = ({ quote, name, title, imgSrc }) => (
  <div className="bg-white p-10 rounded-xl shadow-lg">
    <div className="flex items-center mb-5">
      {[...Array(5)].map((_, i) => (
        <img key={i} src={imgStar1} alt="star rating" className="h-6 w-6" />
      ))}
    </div>
    <p className="text-xl text-[#484848] leading-relaxed mb-8">{quote}</p>
    <div className="flex items-center">
      <img src={imgSrc} alt={name} className="w-16 h-16 rounded-full mr-5" />
      <div>
        <p className="text-2xl font-bold text-gray-900">{name}</p>
        <p className="text-lg text-gray-500">{title}</p>
      </div>
    </div>
  </div>
);

export const NewTestimonials: React.FC = () => {
  const testimonials = [
    {
      quote: '"Ethos Prompt’s AI agent is basically our new virtual receptionist. It never misses a message, always replies with context, and can even handle rescheduling. Clients are impressed — and so are we."',
      name: 'Sarah L.',
      title: 'Marketing Agency Owner',
      imgSrc: imgCloseUpShotSmilingGladWomanWithShortHairstyleWearsRedRimedSpectaclesDressedCasuallyIsolatedWhiteWallExpressesPositiveFeelingsPeopleBeautyConcept1,
    },
    {
      quote: '"The automation is seamless. From lead capture to appointment reminders, everything runs on autopilot. My team now has more time to focus on creative work instead of answering the same questions over and over."',
      name: 'Ryan G.',
      title: 'UX Design Agency',
      imgSrc: img816312,
    },
  ];

  return (
    <section className="py-32 bg-[#F2F2F2]">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-24">
          <h2 className="text-6xl font-bold text-[#030823]">
            Trusted by Businesses Worldwide
          </h2>
        </div>
        <div className="grid md:grid-cols-2 gap-16">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard key={index} {...testimonial} />
          ))}
        </div>
      </div>
    </section>
  );
};
