import React from 'react';
import imgBrain11 from '../../assets/figma/aecd548cefa05584ea3adc21511901f481922d21.png';

export const NewFooter: React.FC = () => {
  return (
    <footer className="bg-white pt-24 pb-12">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-12">
          <div className="col-span-1 md:col-span-2">
            <a href="/" className="flex items-center space-x-3">
              <img className="h-10 w-10" src={imgBrain11} alt="EthosPrompt Logo" />
              <span className="text-4xl font-bold text-[#030823]">EthosPrompt</span>
            </a>
            <p className="mt-6 text-xl text-gray-500 leading-relaxed">
              Intelligent AI agents for modern business.
            </p>
          </div>
          <div>
            <h3 className="text-2xl font-semibold text-gray-900">Product</h3>
            <ul className="mt-5 space-y-3">
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Features</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Pricing</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Integrations</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">API</a></li>
            </ul>
          </div>
          <div>
            <h3 className="text-2xl font-semibold text-gray-900">Company</h3>
            <ul className="mt-5 space-y-3">
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">About Us</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Careers</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Blog</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Contact</a></li>
            </ul>
          </div>
          <div>
            <h3 className="text-2xl font-semibold text-gray-900">Resources</h3>
            <ul className="mt-5 space-y-3">
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Documentation</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Prompting Guide</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Support</a></li>
              <li><a href="#" className="text-xl text-gray-500 hover:text-gray-900">Privacy Policy</a></li>
            </ul>
          </div>
        </div>
        <div className="mt-20 pt-10 border-t border-gray-200 text-center text-xl text-gray-500">
          <p>&copy; {new Date().getFullYear()} EthosPrompt. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};
