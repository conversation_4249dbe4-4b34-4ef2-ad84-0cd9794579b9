import React from 'react';
import { Star } from 'lucide-react';

export const TestimonialsSection: React.FC = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'CTO at TechCorp',
      avatar: '/assets/landing/images/testimonials/avatar-1.jpg',
      content: 'PromptLibrary has revolutionized how we build AI features. Our development speed has increased 10x.',
      rating: 5
    },
    {
      name: '<PERSON>',
      role: 'Lead AI Engineer',
      avatar: '/assets/landing/images/testimonials/avatar-2.jpg',
      content: 'The collaboration features are incredible. Our team can now work together seamlessly on AI projects.',
      rating: 5
    },
    {
      name: '<PERSON>',
      role: 'Product Manager',
      avatar: '/assets/landing/images/testimonials/avatar-3.jpg',
      content: 'The analytics help us understand which prompts work best. It\'s like having a crystal ball for AI performance.',
      rating: 5
    }
  ];

  return (
    <section id="testimonials" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Loved by Teams Worldwide
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See what our customers have to say about their experience with PromptLibrary.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-gray-50 rounded-2xl p-6">
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              
              <p className="text-gray-700 mb-6 italic">"{testimonial.content}"</p>
              
              <div className="flex items-center">
                <img 
                  src={testimonial.avatar} 
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-gray-600 text-sm">{testimonial.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
