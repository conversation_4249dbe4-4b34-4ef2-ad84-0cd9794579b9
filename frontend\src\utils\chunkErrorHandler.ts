/**
 * Chunk Error Handler
 * Handles dynamic import failures and provides fallback mechanisms
 */

export class ChunkErrorHandler {
  private static retryCount = new Map<string, number>();
  private static maxRetries = 3;

  /**
   * Handle chunk loading errors with automatic retry and cache clearing
   */
  static async handleChunkError(error: Error, chunkName?: string): Promise<void> {
    console.error('🚨 Chunk loading error:', error);
    
    const errorKey = chunkName || error.message;
    const currentRetries = this.retryCount.get(errorKey) || 0;
    
    if (currentRetries < this.maxRetries) {
      console.log(`🔄 Retrying chunk load (${currentRetries + 1}/${this.maxRetries})`);
      this.retryCount.set(errorKey, currentRetries + 1);
      
      // Clear caches and retry
      await this.clearCaches();
      
      // Wait a bit before retry
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Force reload if this is the last retry
      if (currentRetries + 1 >= this.maxRetries) {
        console.log('🔄 Max retries reached, forcing page reload');
        window.location.reload();
      }
    } else {
      console.error('❌ Max retries exceeded for chunk loading');
      this.showErrorMessage();
    }
  }

  /**
   * Clear all caches to resolve stale chunk issues
   */
  private static async clearCaches(): Promise<void> {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
        console.log('🧹 Cleared caches:', cacheNames);
      }
      
      // Clear service worker if available
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        await Promise.all(registrations.map(reg => reg.unregister()));
        console.log('🔧 Unregistered service workers');
      }
    } catch (error) {
      console.warn('⚠️ Failed to clear caches:', error);
    }
  }

  /**
   * Show user-friendly error message
   */
  private static showErrorMessage(): void {
    const errorDiv = document.createElement('div');
    errorDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        font-family: system-ui, sans-serif;
      ">
        <div style="
          background: white;
          padding: 30px;
          border-radius: 8px;
          max-width: 500px;
          text-align: center;
          box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        ">
          <h2 style="color: #dc2626; margin-bottom: 16px;">⚠️ Loading Error</h2>
          <p style="color: #374151; margin-bottom: 20px;">
            There was an issue loading part of the application. This is usually caused by cached files.
          </p>
          <button onclick="window.location.reload()" style="
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
          ">
            Reload Page
          </button>
          <button onclick="this.parentElement.parentElement.remove()" style="
            background: #6b7280;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
          ">
            Dismiss
          </button>
        </div>
      </div>
    `;
    document.body.appendChild(errorDiv);
  }

  /**
   * Wrap dynamic imports with error handling
   */
  static async safeImport<T>(importFn: () => Promise<T>, chunkName?: string): Promise<T> {
    try {
      return await importFn();
    } catch (error) {
      await this.handleChunkError(error as Error, chunkName);
      throw error; // Re-throw to let React handle it
    }
  }

  /**
   * Initialize global error handlers
   */
  static initialize(): void {
    // Handle unhandled promise rejections (chunk loading failures)
    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason?.message?.includes('Loading chunk') || 
          event.reason?.message?.includes('Failed to fetch dynamically imported module')) {
        console.log('🔧 Handling chunk loading rejection');
        event.preventDefault();
        this.handleChunkError(event.reason, 'dynamic-import');
      }
    });

    // Handle general script loading errors
    window.addEventListener('error', (event) => {
      if (event.filename?.includes('.js') && event.message?.includes('Loading')) {
        console.log('🔧 Handling script loading error');
        this.handleChunkError(new Error(event.message), 'script-load');
      }
    });

    console.log('🛡️ Chunk error handler initialized');
  }
}

// Auto-initialize
ChunkErrorHandler.initialize();
