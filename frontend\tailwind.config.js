/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme');

module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      'xs': '375px',
      ...defaultTheme.screens,
      '3xl': '1920px',
    },
    extend: {
      // Color Palette
      colors: {
        // Primary Colors
        primary: {
          dark: '#0D1144',     // Deep blue
          DEFAULT: '#7409C5',  // Purple
          light: '#8235F4',    // Lighter purple
        },
        
        // Gradient Colors
        gradient: {
          start: '#7471E0',    // Soft purple
          end: '#EA73D4',      // Pink
        },
        
        // Text Colors
        text: {
          primary: '#0D1144',  // Dark blue
          secondary: '#717493', // Grayish blue
          tertiary: '#4B5563', // Cool gray
        },
        
        // Background Colors
        background: {
          light: '#F9FAFB',    // Light gray
          DEFAULT: '#FFFFFF',  // White
          dark: '#F3F4F6',     // Slightly darker gray
        },
        
        // Border Colors
        border: {
          light: '#E5E7EB',    // Light gray border
          DEFAULT: '#D1D5DB',  // Medium gray border
        },
      },
      
      // Typography
      fontFamily: {
        sans: ['Inter', ...defaultTheme.fontFamily.sans],
        display: ['Inter', 'sans-serif'],
      },
      
      fontSize: {
        'display-2xl': ['4.5rem', { lineHeight: '1', letterSpacing: '-0.02em' }],
        'display-xl': ['3.75rem', { lineHeight: '1', letterSpacing: '-0.02em' }],
        'display-lg': ['3rem', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
        'display-md': ['2.25rem', { lineHeight: '2.5rem', letterSpacing: '-0.02em' }],
        'display-sm': ['1.875rem', { lineHeight: '2.25rem' }],
        'display-xs': ['1.5rem', { lineHeight: '2rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'xs': ['0.75rem', { lineHeight: '1rem' }],
      },
      
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
        extrabold: '800',
      },
      
      // Spacing
      spacing: {
        '4.5': '1.125rem',    // 18px
        '13': '3.25rem',      // 52px
        '15': '3.75rem',      // 60px
        '18': '4.5rem',       // 72px
        '22': '5.5rem',       // 88px
        '26': '6.5rem',       // 104px
        '30': '7.5rem',       // 120px
        '34': '8.5rem',       // 136px
        '38': '9.5rem',       // 152px
      },
      
      // Border Radius
      borderRadius: {
        '4xl': '2rem',
        '5xl': '2.5rem',
      },
      
      // Box Shadow
      boxShadow: {
        'card': '0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.025)',
        'card-hover': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'button': '0 4px 6px -1px rgba(116, 9, 197, 0.2), 0 2px 4px -1px rgba(116, 9, 197, 0.06)',
        'button-hover': '0 10px 15px -3px rgba(116, 9, 197, 0.3), 0 4px 6px -2px rgba(116, 9, 197, 0.1)',
      },
      
      // Gradients
      backgroundImage: {
        'primary-gradient': 'linear-gradient(90deg, #7471E0 0%, #EA73D4 100%)',
        'text-gradient': 'linear-gradient(90deg, #0D1144 0%, #717493 100%)',
        'card-gradient': 'linear-gradient(180deg, #FFFFFF 0%, #F9FAFB 100%)',
      },
      
      // Animation
      animation: {
        'shimmer': 'shimmer 2s linear infinite',
        'wave': 'wave 1.5s ease-in-out infinite',
        'float': 'float 6s ease-in-out infinite',
        'slide-in-right': 'slideInRight 0.5s ease-out',
        'slide-in-left': 'slideInLeft 0.5s ease-out',
        'fade-in-up': 'fadeInUp 0.6s ease-out',
        'fade-in-down': 'fadeInDown 0.6s ease-out',
      },
      
      keyframes: {
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        wave: {
          '0%, 60%, 100%': { transform: 'initial' },
          '30%': { transform: 'translateY(-15px)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        fadeInUp: {
          '0%': { transform: 'translateY(30px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        fadeInDown: {
          '0%': { transform: 'translateY(-30px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
      
      // Transition
      transitionProperty: {
        'transform': 'transform',
        'opacity': 'opacity',
        'colors': 'background-color, border-color, color, fill, stroke',
        'shadow': 'box-shadow',
      },
      
      // Z-index
      zIndex: {
        '1': '1',
        '2': '2',
        '3': '3',
        '4': '4',
        '5': '5',
        'max': '9999',
      },
    },
  },
  
  // Variants
  variants: {
    extend: {
      opacity: ['disabled'],
      scale: ['group-hover'],
      translate: ['group-hover'],
      backgroundColor: ['active', 'group-hover'],
      textColor: ['active', 'group-hover'],
      borderColor: ['active', 'group-hover'],
      boxShadow: ['active', 'group-hover'],
    },
  },
  
  // Core Plugins
  corePlugins: {
    container: false, // Disable default container
  },
  
  // Plugins
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/line-clamp'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
