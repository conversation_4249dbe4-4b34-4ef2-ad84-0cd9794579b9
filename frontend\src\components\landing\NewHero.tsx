import React from 'react';
import imgImage24 from '../../assets/figma/e47d38d67ca45b017f89a9bf9d00f125ce1ceae6.png';

export const NewHero: React.FC = () => {
  return (
    <section className="relative pt-64 pb-40 text-center">
      <div className="absolute inset-0 bg-gradient-to-b from-white to-[#E8E8E8] z-0"></div>
      <div className="relative max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
        <h1 className="text-8xl font-bold tracking-[-4.05px]">
          <span
            className="bg-clip-text text-transparent bg-gradient-to-r from-[#0d1144] to-[#717493]"
          >Transform your Business </span>
          <br />
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0d1144] to-[#717493]">with</span>
          <span className="text-[#7409c5]"> AI Agents</span>
        </h1>
        <p className="mt-8 max-w-4xl mx-auto text-2xl text-[#484848] leading-relaxed">
          Automate your customer support, sales, and internal workflows with intelligent AI agents that learn from your knowledge base and provide instant, accurate responses.
        </p>
        <div className="mt-12 flex justify-center space-x-6">
          <a
            href="#"
            className="px-10 py-5 bg-gradient-to-b from-[#D47CD9] to-[#7409C5] text-white text-2xl font-semibold rounded-lg shadow-lg"
          >
            Get Started for Free
          </a>
          <a
            href="#"
            className="px-10 py-5 bg-white text-[#030823] text-2xl font-semibold rounded-lg border-2 border-gray-300 shadow-lg"
          >
            Request a Demo
          </a>
        </div>
        <div className="mt-24">
          <img className="mx-auto rounded-xl shadow-2xl" src={imgImage24} alt="AI Agent Dashboard Preview" />
        </div>
      </div>
    </section>
  );
};
