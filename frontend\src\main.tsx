import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { initializeMonitoring } from './utils/monitoring'
import { ChunkErrorHandler } from './utils/chunkErrorHandler'

// Import Firebase test functions for debugging
import { testFirebaseConnection } from './test-firebase-connection.js'
import { runComprehensiveTest } from './debug-prompt-saving.js'

// Initialize chunk error handling
ChunkErrorHandler.initialize()

// Register service worker for offline support and caching with cache invalidation
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', async () => {
    try {
      // Check for existing registrations and update if needed
      const existingRegistration = await navigator.serviceWorker.getRegistration();

      if (existingRegistration) {
        // Force update to get the latest service worker
        await existingRegistration.update();
        console.log('SW updated: ', existingRegistration);
      } else {
        // Register new service worker
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('SW registered: ', registration);
      }

      // Listen for service worker updates
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('SW controller changed - reloading page');
        window.location.reload();
      });

    } catch (error) {
      console.log('SW registration failed: ', error);
    }
  });
}

// Initialize monitoring system
if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_MONITORING === 'true') {
  initializeMonitoring();
}

// Make test functions available globally for debugging
if (import.meta.env.DEV) {
  window.testFirebaseConnection = testFirebaseConnection;
  console.log('🧪 Firebase test functions loaded for debugging');
  console.log('🔧 Enhanced debug tools available via window.debugPromptSaving');
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
