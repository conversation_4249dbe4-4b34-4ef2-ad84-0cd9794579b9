import React from 'react';
import imgImage28 from '../../assets/figma/4274b92678da422b4e60a0b832dcdb3fbc206917.png';

export const NewHowItWorks: React.FC = () => {
  return (
    <section className="py-32 bg-[#F2F2F2]">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-6xl font-bold text-[#030823]">How It Works</h2>
          <p className="mt-6 max-w-3xl mx-auto text-2xl text-[#484848] leading-relaxed">
            Get started with our AI agents in just a few simple steps.
          </p>
        </div>
        <div className="mt-20 flex flex-col md:flex-row items-center justify-between gap-16">
          <div className="md:w-1/2 space-y-12">
            <div className="flex items-start">
              <div className="flex-shrink-0 h-16 w-16 rounded-full bg-gradient-to-b from-[#D47CD9] to-[#7409C5] text-white flex items-center justify-center text-3xl font-bold">
                1
              </div>
              <div className="ml-8">
                <h3 className="text-4xl font-bold text-[#030823]">Connect Your Knowledge Base</h3>
                <p className="mt-4 text-xl text-[#484848] leading-relaxed">
                  Upload your documents, websites, or other data sources to our platform.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 h-16 w-16 rounded-full bg-gradient-to-b from-[#D47CD9] to-[#7409C5] text-white flex items-center justify-center text-3xl font-bold">
                2
              </div>
              <div className="ml-8">
                <h3 className="text-4xl font-bold text-[#030823]">Customize Your AI Agent</h3>
                <p className="mt-4 text-xl text-[#484848] leading-relaxed">
                  Define the agent's personality, tone, and behavior to match your brand.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 h-16 w-16 rounded-full bg-gradient-to-b from-[#D47CD9] to-[#7409C5] text-white flex items-center justify-center text-3xl font-bold">
                3
              </div>
              <div className="ml-8">
                <h3 className="text-4xl font-bold text-[#030823]">Deploy to Your Channels</h3>
                <p className="mt-4 text-xl text-[#484848] leading-relaxed">
                  Integrate your AI agent into your website, app, or other platforms.
                </p>
              </div>
            </div>
          </div>
          <div className="md:w-1/2 mt-12 md:mt-0">
            <img className="rounded-xl shadow-2xl" src={imgImage28} alt="How it works illustration" />
          </div>
        </div>
      </div>
    </section>
  );
};
