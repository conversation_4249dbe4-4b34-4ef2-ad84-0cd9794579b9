import React from 'react';
import { motion, Variants, Transition } from 'framer-motion';

// Import assets with proper type annotations
const assets = {
  backgroundPattern: "/assets/landing/e47d38d67ca45b017f89a9bf9d00f125ce1ceae6.png",
  aiIllustration: "/assets/landing/4274b92678da422b4e60a0b832dcdb3fbc206917.png",
  serviceBadge: "/assets/landing/ec64f6b61587e04b374397f852916b0abdfc6c3a.png",
  chartImage: "/assets/landing/ac8560907285b10661a9de9c3f7f9c60db71090a.png",
  teamMembers: [
    "/assets/landing/3806640888f4b86f1967fd9430c374d3ff9b1d15.png",
    "/assets/landing/4f61ddb6debb5a8b6682079847ce4f0ee077d26a.png",
    "/assets/landing/cc1d39e1afc978e28f8ca4f3b48ccb29b63fdb25.png",
    "/assets/landing/96d245ea65a7fa752b2f53e2f411cd81f3453d59.png"
  ]
};

// Animation variants with proper TypeScript types
const fadeInUp: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.6, 
      ease: [0.16, 1, 0.3, 1] as any // Using any to bypass the Easing type issue
    } as Transition
  }
};

const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

type StatCardProps = {
  value: string;
  title: string;
  description: string;
  delay?: number;
};

const StatCard: React.FC<StatCardProps> = ({ value, title, description, delay = 0 }) => (
  <motion.div
    variants={fadeInUp}
    initial="hidden"
    whileInView="visible"
    viewport={{ once: true, margin: "-20% 0px -20% 0px" }}
    transition={{ delay }}
    className="bg-background rounded-3xl p-8 shadow-card hover:shadow-card-hover transition-shadow duration-300"
  >
    <p className="text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gradient-start to-gradient-end bg-clip-text text-transparent mb-4">
      {value}
    </p>
    <h3 className="text-xl text-text-primary font-medium mb-2">{title}</h3>
    <p className="text-text-tertiary">{description}</p>
  </motion.div>
);

type FloatingCardProps = {
  className?: string;
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  position?: {
    base?: string;
    lg: string;
  };
  delay?: number;
};

const FloatingCard: React.FC<FloatingCardProps> = ({
  className = '',
  title,
  subtitle,
  icon,
  children,
  position = { base: 'static', lg: 'absolute' },
  delay = 0
}) => (
  <motion.div
    className={`bg-gradient-to-b from-background to-background-dark/80 rounded-3xl shadow-card p-6 ${className}`}
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6, 
        ease: "easeOut",
        delay,
        type: 'spring',
        stiffness: 100
      } 
    }}
    viewport={{ once: true, margin: "-20% 0px -20% 0px" }}
    whileHover={{ 
      y: -5,
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
    }}
    style={{
      position: position.base === 'absolute' ? 'absolute' : 'static',
      ...(position.lg === 'absolute' ? {
        '@media (min-width: 1024px)': {
          position: 'absolute'
        }
      } : {})
    }}
  >
    {icon && <div className="mb-4">{icon}</div>}
    <h3 className="text-xl font-semibold bg-gradient-to-r from-gradient-start to-gradient-end bg-clip-text text-transparent">
      {title}
    </h3>
    {subtitle && <p className="text-text-secondary mt-1">{subtitle}</p>}
    {children}
  </motion.div>
);

// Team member avatars
const teamMembers = [
  { id: 1, src: "/assets/landing/3806640888f4b86f1967fd9430c374d3ff9b1d15.png", alt: "Team member 1" },
  { id: 2, src: "/assets/landing/4f61ddb6debb5a8b6682079847ce4f0ee077d26a.png", alt: "Team member 2" },
  { id: 3, src: "/assets/landing/cc1d39e1afc978e28f8ca4f3b48ccb29b63fdb25.png", alt: "Team member 3" },
  { id: 4, src: "/assets/landing/96d245ea65a7fa752b2f53e2f411cd81f3453d59.png", alt: "Team member 4" }
];

export const HeroSection: React.FC = () => {
  const stats = [
    { value: "10K+", title: "Active Users", description: "and growing every day" },
    { value: "95%", title: "Satisfaction", description: "from our customers" },
    { value: "24/7", title: "Support", description: "always here to help" }
  ];

  return (
    <section className="relative w-full bg-white pt-24 pb-12 md:pt-32 md:pb-24 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-b from-white via-gray-50 to-gray-200" />
        <img
          src={imgImage24}
          alt="Abstract background pattern"
          className="absolute top-0 left-1/2 -translate-x-1/2 w-full max-w-[1600px] opacity-50 pointer-events-none"
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center px-4 sm:px-6 lg:px-8">
        {/* Headline */}
        <div className="text-center max-w-5xl mx-auto">
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-semibold tracking-tighter">
            <span className="bg-clip-text bg-gradient-to-r from-[#0d1144] to-[#717493] text-transparent">Transform your Business </span>
            <span className="bg-clip-text bg-gradient-to-r from-[#0d1144] to-[#717493] text-transparent">with </span>
            <span className="text-[#7409c5]">AI Agents</span>
          </h1>
        </div>

        {/* Illustration and Floating Cards Section */}
        <div className="relative mt-12 lg:mt-20 w-full max-w-6xl mx-auto">
          {/* Central AI Agent Illustration */}
          <div className="relative z-10 w-full max-w-3xl mx-auto">
            <img src={imgAdobeExpressFile1} alt="AI agent illustration" className="w-full h-auto shadow-2xl rounded-3xl" />
          </div>

          {/* Floating Cards - Responsive Layout */}
          <div className="lg:absolute lg:inset-0 flex flex-col lg:flex-none items-center gap-8 mt-8 lg:mt-0">
            {/* Cards that are absolutely positioned on desktop */}
            <div className="contents lg:relative lg:block w-full h-full">
              {/* Business Forecast */}
              <div className="w-full max-w-md lg:absolute lg:top-[5%] lg:left-[5%] lg:w-[30%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6">
                <img src={imgImage23} alt="Business Forecast chart" className="w-full h-auto mb-4" />
                <h3 className="text-xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent">Business Forecast</h3>
                <p className="text-gray-600">Created by Ethos</p>
              </div>

              {/* Automated Service */}
              <div className="w-full max-w-md lg:absolute lg:top-[15%] lg:right-0 lg:w-[25%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6 flex flex-col items-center text-center">
                <div className="relative size-32 mb-4">
                  <img src={imgEllipse51} alt="24/7 service icon background" className="w-full h-full" />
                  <span className="absolute inset-0 flex items-center justify-center text-4xl font-medium text-gray-700">24/7</span>
                </div>
                <h3 className="text-xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent">Automated Service & Appointments</h3>
              </div>

              {/* Task Automation */}
              <div className="w-full max-w-md lg:absolute lg:bottom-[20%] lg:right-[2%] lg:w-[35%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6">
                <h3 className="text-2xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent">Task Automation</h3>
                <p className="text-gray-600">and improved efficiency</p>
              </div>

              {/* 600+ Integrations */}
              <div className="w-full max-w-md lg:absolute lg:bottom-[25%] lg:left-0 lg:w-[25%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6">
                <h3 className="text-4xl font-bold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent">600+</h3>
                <p className="text-xl text-gray-600">External App integration</p>
              </div>
              
              {/* Smarter Staff */}
              <div className="w-full max-w-md lg:absolute lg:bottom-0 lg:left-1/2 lg:-translate-x-1/2 lg:w-[35%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6">
                 <div className="flex justify-center space-x-[-1rem] mb-4">
                    <img className="size-16 rounded-full border-2 border-white" src={imgPortraitMiddleAgedBusinesswoman1} alt="staff member 1" />
                    <img className="size-16 rounded-full border-2 border-white" src={img816312} alt="staff member 2" />
                    <img className="size-16 rounded-full border-2 border-white" src={imgYoungBeardedManWithStripedShirt1} alt="staff member 3" />
                    <img className="size-16 rounded-full border-2 border-white" src={imgCloseUpShotSmilingGladWomanWithShortHairstyleWearsRedRimedSpectaclesDressedCasuallyIsolatedWhiteWallExpressesPositiveFeelingsPeopleBeautyConcept1} alt="staff member 4" />
                </div>
                <p className="text-center text-xl text-gray-600">for a smarter staff</p>
              </div>
            </div>
          </div>
        </div>

        {/* Proven Results Section */}
        <div className="w-full max-w-7xl mx-auto mt-20 pt-16 lg:mt-32">
          <h2 className="text-4xl md:text-5xl font-semibold text-center tracking-tight mb-12">
              <span className="bg-clip-text bg-gradient-to-r from-[#0d1144] to-[#717493] text-transparent">Proven results for </span>
              <span className="text-[#7409c5]">Your Business</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Stat Card 1 */}
              <div className="bg-white rounded-3xl p-8 shadow-lg text-center">
                  <p className="text-6xl lg:text-7xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent mb-4">30%</p>
                  <h3 className="text-xl text-gray-800 font-medium mb-2">Increased sales rates</h3>
                  <p className="text-gray-600">AI ensures automatic task handling, scheduling, and optimizes lead-to-customer conversations.</p>
              </div>
              {/* Stat Card 2 */}
              <div className="bg-white rounded-3xl p-8 shadow-lg text-center">
                  <p className="text-6xl lg:text-7xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent mb-4">80%</p>
                  <h3 className="text-xl text-gray-800 font-medium mb-2">Cutoff manual and repetitive work</h3>
                  <p className="text-gray-600">AI agents can massively reduce manual and repetitive work.</p>
              </div>
              {/* Stat Card 3 */}
              <div className="bg-white rounded-3xl p-8 shadow-lg text-center">
                  <p className="text-6xl lg:text-7xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent mb-4">40%</p>
                  <h3 className="text-xl text-gray-800 font-medium mb-2">Leverage your cost savings</h3>
                  <p className="text-gray-600">Increased sales and lowered overheads provide significant cost savings.</p>
              </div>
              {/* CTA Card */}
              <div className="bg-white rounded-3xl p-8 shadow-lg flex flex-col items-center justify-center text-center">
                  <h3 className="text-xl text-gray-800 font-medium mb-6">See how your Business can achieve these results</h3>
                  <a href="#contact" className="w-full px-6 py-3 bg-[#8235f4] text-white font-semibold rounded-full shadow-lg hover:bg-[#7409c5] transition">
                      Speak to our Specialist
                  </a>
              </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;

