import React from 'react';
import { motion } from 'framer-motion';
import type { Variants, Transition } from 'framer-motion';

// Asset paths
const ASSETS = {
  backgroundPattern: "/assets/landing/e47d38d67ca45b017f89a9bf9d00f125ce1ceae6.png",
  aiIllustration: "/assets/landing/4274b92678da422b4e60a0b832dcdb3fbc206917.png",
  serviceBadge: "/assets/landing/ec64f6b61587e04b374397f852916b0abdfc6c3a.png",
  chartImage: "/assets/landing/ac8560907285b10661a9de9c3f7f9c60db71090a.png"
} as const;

// Animation variants with proper TypeScript types
const fadeInUp: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.6, 
      ease: [0.16, 1, 0.3, 1] as any // Using any to bypass the Easing type issue
    } as Transition
  }
};

const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

// Stat item type for the statistics section
type StatItem = {
  value: string;
  title: string;
  description: string;
};

// Team member type
type TeamMember = {
  id: number;
  src: string;
  alt: string;
};

type FloatingCardProps = {
  className?: string;
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  position?: {
    base?: string;
    lg: string;
  };
  delay?: number;
};

const FloatingCard: React.FC<FloatingCardProps> = ({
  className = '',
  title,
  subtitle,
  icon,
  children,
  position = { base: 'static', lg: 'absolute' },
  delay = 0
}) => (
  <motion.div
    className={`bg-gradient-to-b from-background to-background-dark/80 rounded-3xl shadow-card p-6 ${className}`}
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6, 
        ease: "easeOut",
        delay,
        type: 'spring',
        stiffness: 100
      } 
    }}
    viewport={{ once: true, margin: "-20% 0px -20% 0px" }}
    whileHover={{ 
      y: -5,
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
    }}
    style={{
      position: position.base === 'absolute' ? 'absolute' : 'static',
      ...(position.lg === 'absolute' ? {
        '@media (min-width: 1024px)': {
          position: 'absolute'
        }
      } : {})
    }}
  >
    {icon && <div className="mb-4">{icon}</div>}
    <h3 className="text-xl font-semibold bg-gradient-to-r from-gradient-start to-gradient-end bg-clip-text text-transparent">
      {title}
    </h3>
    {subtitle && <p className="text-text-secondary mt-1">{subtitle}</p>}
    {children}
  </motion.div>
);

// Team member avatars
const TEAM_MEMBERS: TeamMember[] = [
  { id: 1, src: "/assets/landing/3806640888f4b86f1967fd9430c374d3ff9b1d15.png", alt: "Team member 1" },
  { id: 2, src: "/assets/landing/4f61ddb6debb5a8b6682079847ce4f0ee077d26a.png", alt: "Team member 2" },
  { id: 3, src: "/assets/landing/cc1d39e1afc978e28f8ca4f3b48ccb29b63fdb25.png", alt: "Team member 3" },
  { id: 4, src: "/assets/landing/96d245ea65a7fa752b2f53e2f411cd81f3453d59.png", alt: "Team member 4" }
];

// Stats data
const STATS: StatItem[] = [
  { value: "10K+", title: "Active Users", description: "and growing every day" },
  { value: "95%", title: "Satisfaction", description: "from our customers" },
  { value: "24/7", title: "Support", description: "always here to help" }
];

export const HeroSection: React.FC = () => {
  return (
    <section className="relative w-full bg-white pt-24 pb-12 md:pt-32 md:pb-24 overflow-hidden">
      {/* Background Pattern */}
      <div 
        className="absolute inset-0 w-full h-full opacity-5"
        style={{
          backgroundImage: `url(${ASSETS.backgroundPattern})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      />
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white via-white/90 to-white/70" />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="text-center lg:text-left"
          >
            <motion.div variants={fadeInUp} className="mb-6">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
                <span>✨</span>
                <span className="ml-2">AI-Powered Solutions</span>
              </div>
            </motion.div>
            
            <motion.h1 
              variants={fadeInUp}
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6"
            >
              Transform Your Business with <span className="text-primary">AI</span>
            </motion.h1>
            
            <motion.p 
              variants={fadeInUp}
              className="text-lg md:text-xl text-gray-600 mb-8 max-w-xl mx-auto lg:mx-0"
            >
              Harness the power of artificial intelligence to streamline operations, enhance customer experiences, and drive growth.
            </motion.p>
            
            <motion.div 
              variants={fadeInUp}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12"
            >
              <button className="px-8 py-4 bg-primary hover:bg-primary-dark text-white font-medium rounded-lg transition-colors duration-300">
                Get Started Free
              </button>
              <button className="px-8 py-4 border-2 border-gray-200 hover:border-primary text-gray-700 font-medium rounded-lg transition-colors duration-300">
                Watch Demo
              </button>
            </motion.div>
            
            {/* Stats */}
            <motion.div 
              variants={staggerContainer}
              className="grid grid-cols-3 gap-4 mt-12"
            >
              {STATS.map((stat) => (
                <div key={stat.title} className="text-center">
                  <motion.p 
                    variants={fadeInUp}
                    className="text-3xl font-bold text-primary mb-1"
                  >
                    {stat.value}
                  </motion.p>
                  <motion.p 
                    variants={fadeInUp}
                    className="text-sm text-gray-600"
                  >
                    {stat.title}
                  </motion.p>
                </div>
              ))}
            </motion.div>
          </motion.div>
          
          {/* Right Column - Illustration */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, ease: [0.16, 1, 0.3, 1] }}
            className="relative"
          >
            <div className="relative z-10">
              <img 
                src={ASSETS.aiIllustration} 
                alt="AI Illustration" 
                className="w-full h-auto max-w-xl mx-auto"
              />
              
              {/* Floating Elements */}
              <FloatingCard 
                title="AI-Powered Analytics"
                subtitle="Real-time insights"
                className="absolute -left-4 -top-8 w-40"
                position={{ base: 'static', lg: 'absolute' }}
                delay={0.2}
              >
                <img 
                  src={ASSETS.chartImage} 
                  alt="Analytics Chart" 
                  className="w-full h-auto mt-2"
                />
              </FloatingCard>
              
              <FloatingCard 
                title="Trusted by 100+ Teams"
                className="absolute -right-4 top-1/4 w-48"
                position={{ base: 'static', lg: 'absolute' }}
                delay={0.4}
              >
                <div className="flex -space-x-2 mt-3">
                  {TEAM_MEMBERS.slice(0, 4).map(member => (
                    <img 
                      key={member.id}
                      src={member.src} 
                      alt={member.alt} 
                      className="w-8 h-8 rounded-full border-2 border-white"
                    />
                  ))}
                </div>
              </FloatingCard>
              
              <FloatingCard 
                title="99.9% Uptime"
                subtitle="Reliable Service"
                className="absolute left-1/2 -translate-x-1/2 -bottom-8 w-40"
                position={{ base: 'static', lg: 'absolute' }}
                delay={0.6}
              >
                <div className="flex items-center justify-center mt-2">
                  <img 
                    src={ASSETS.serviceBadge} 
                    alt="Service Badge" 
                    className="w-8 h-8"
                  />
                </div>
              </FloatingCard>
            </div>
          </motion.div>
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute top-0 right-0 w-1/3 h-1/2 bg-gradient-to-br from-primary/10 to-transparent rounded-full filter blur-3xl -z-10" />
      <div className="absolute bottom-0 left-0 w-1/2 h-1/3 bg-gradient-to-tr from-secondary/10 to-transparent rounded-full filter blur-3xl -z-10" />
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-b from-white via-gray-50 to-gray-200" />
          src={imgImage24}
          alt="Abstract background pattern"
          className="absolute top-0 left-1/2 -translate-x-1/2 w-full max-w-[1600px] opacity-50 pointer-events-none"
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center px-4 sm:px-6 lg:px-8">
        {/* Headline */}
        <div className="text-center max-w-5xl mx-auto">
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-semibold tracking-tighter">
            <span className="bg-clip-text bg-gradient-to-r from-[#0d1144] to-[#717493] text-transparent">Transform your Business </span>
            <span className="bg-clip-text bg-gradient-to-r from-[#0d1144] to-[#717493] text-transparent">with </span>
            <span className="text-[#7409c5]">AI Agents</span>
          </h1>
        </div>

        {/* Illustration and Floating Cards Section */}
        <div className="relative mt-12 lg:mt-20 w-full max-w-6xl mx-auto">
          {/* Central AI Agent Illustration */}
          <div className="relative z-10 w-full max-w-3xl mx-auto">
            <img src={imgAdobeExpressFile1} alt="AI agent illustration" className="w-full h-auto shadow-2xl rounded-3xl" />
          </div>

          {/* Floating Cards - Responsive Layout */}
          <div className="lg:absolute lg:inset-0 flex flex-col lg:flex-none items-center gap-8 mt-8 lg:mt-0">
            {/* Cards that are absolutely positioned on desktop */}
            <div className="contents lg:relative lg:block w-full h-full">
              {/* Business Forecast */}
              <div className="w-full max-w-md lg:absolute lg:top-[5%] lg:left-[5%] lg:w-[30%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6">
                <img src={imgImage23} alt="Business Forecast chart" className="w-full h-auto mb-4" />
                <h3 className="text-xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent">Business Forecast</h3>
                <p className="text-gray-600">Created by Ethos</p>
              </div>

              {/* Automated Service */}
              <div className="w-full max-w-md lg:absolute lg:top-[15%] lg:right-0 lg:w-[25%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6 flex flex-col items-center text-center">
                <div className="relative size-32 mb-4">
                  <img src={imgEllipse51} alt="24/7 service icon background" className="w-full h-full" />
                  <span className="absolute inset-0 flex items-center justify-center text-4xl font-medium text-gray-700">24/7</span>
                </div>
                <h3 className="text-xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent">Automated Service & Appointments</h3>
              </div>

              {/* Task Automation */}
              <div className="w-full max-w-md lg:absolute lg:bottom-[20%] lg:right-[2%] lg:w-[35%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6">
                <h3 className="text-2xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent">Task Automation</h3>
                <p className="text-gray-600">and improved efficiency</p>
              </div>

              {/* 600+ Integrations */}
              <div className="w-full max-w-md lg:absolute lg:bottom-[25%] lg:left-0 lg:w-[25%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6">
                <h3 className="text-4xl font-bold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent">600+</h3>
                <p className="text-xl text-gray-600">External App integration</p>
              </div>
              
              {/* Smarter Staff */}
              <div className="w-full max-w-md lg:absolute lg:bottom-0 lg:left-1/2 lg:-translate-x-1/2 lg:w-[35%] bg-gradient-to-b from-white to-gray-100 rounded-3xl shadow-lg p-6">
                 <div className="flex justify-center space-x-[-1rem] mb-4">
                    <img className="size-16 rounded-full border-2 border-white" src={imgPortraitMiddleAgedBusinesswoman1} alt="staff member 1" />
                    <img className="size-16 rounded-full border-2 border-white" src={img816312} alt="staff member 2" />
                    <img className="size-16 rounded-full border-2 border-white" src={imgYoungBeardedManWithStripedShirt1} alt="staff member 3" />
                    <img className="size-16 rounded-full border-2 border-white" src={imgCloseUpShotSmilingGladWomanWithShortHairstyleWearsRedRimedSpectaclesDressedCasuallyIsolatedWhiteWallExpressesPositiveFeelingsPeopleBeautyConcept1} alt="staff member 4" />
                </div>
                <p className="text-center text-xl text-gray-600">for a smarter staff</p>
              </div>
            </div>
          </div>
        </div>

        {/* Proven Results Section */}
        <div className="w-full max-w-7xl mx-auto mt-20 pt-16 lg:mt-32">
          <h2 className="text-4xl md:text-5xl font-semibold text-center tracking-tight mb-12">
              <span className="bg-clip-text bg-gradient-to-r from-[#0d1144] to-[#717493] text-transparent">Proven results for </span>
              <span className="text-[#7409c5]">Your Business</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Stat Card 1 */}
              <div className="bg-white rounded-3xl p-8 shadow-lg text-center">
                  <p className="text-6xl lg:text-7xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent mb-4">30%</p>
                  <h3 className="text-xl text-gray-800 font-medium mb-2">Increased sales rates</h3>
                  <p className="text-gray-600">AI ensures automatic task handling, scheduling, and optimizes lead-to-customer conversations.</p>
              </div>
              {/* Stat Card 2 */}
              <div className="bg-white rounded-3xl p-8 shadow-lg text-center">
                  <p className="text-6xl lg:text-7xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent mb-4">80%</p>
                  <h3 className="text-xl text-gray-800 font-medium mb-2">Cutoff manual and repetitive work</h3>
                  <p className="text-gray-600">AI agents can massively reduce manual and repetitive work.</p>
              </div>
              {/* Stat Card 3 */}
              <div className="bg-white rounded-3xl p-8 shadow-lg text-center">
                  <p className="text-6xl lg:text-7xl font-semibold bg-clip-text bg-gradient-to-r from-[#7471e0] to-[#ea73d4] text-transparent mb-4">40%</p>
                  <h3 className="text-xl text-gray-800 font-medium mb-2">Leverage your cost savings</h3>
                  <p className="text-gray-600">Increased sales and lowered overheads provide significant cost savings.</p>
              </div>
              {/* CTA Card */}
              <div className="bg-white rounded-3xl p-8 shadow-lg flex flex-col items-center justify-center text-center">
                  <h3 className="text-xl text-gray-800 font-medium mb-6">See how your Business can achieve these results</h3>
                  <a href="#contact" className="w-full px-6 py-3 bg-[#8235f4] text-white font-semibold rounded-full shadow-lg hover:bg-[#7409c5] transition">
                      Speak to our Specialist
                  </a>
              </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;

