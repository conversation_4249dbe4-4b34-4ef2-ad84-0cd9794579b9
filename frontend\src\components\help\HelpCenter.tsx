/**
 * Enhanced Help Center Component
 * Comprehensive help center with advanced search, multimedia content, accessibility features,
 * and user analytics. Provides intuitive navigation and progressive content disclosure.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  MagnifyingGlassIcon,
  BookOpenIcon,
  QuestionMarkCircleIcon,
  VideoCameraIcon,
  ChatBubbleLeftRightIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ChevronLeftIcon,
  StarIcon,
  ClockIcon,
  EyeIcon,
  TagIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  LightBulbIcon,
  AcademicCapIcon,
  PlayIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
  HomeIcon,
  DocumentTextIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

// Enhanced interfaces for the new Help Center
interface HelpArticle {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  category: string;
  subcategory?: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  lastUpdated: string;
  views: number;
  helpful: number;
  rating: number;
  estimatedReadTime: number;
  type: 'article' | 'video' | 'tutorial' | 'faq' | 'guide' | 'troubleshooting';
  featured: boolean;
  prerequisites?: string[];
  relatedArticles?: string[];
  videoUrl?: string;
  steps?: ArticleStep[];
  faqs?: FAQ[];
}

interface ArticleStep {
  id: string;
  title: string;
  content: string;
  image?: string;
  code?: string;
  tips?: string[];
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  helpful: number;
}

interface SearchFilters {
  difficulty: string[];
  type: string[];
  category: string[];
  tags: string[];
  dateRange: string;
  sortBy: 'relevance' | 'date' | 'popularity' | 'rating';
}

interface HelpCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  articleCount: number;
  subcategories?: HelpSubcategory[];
  featured: boolean;
  color: string;
}

interface HelpSubcategory {
  id: string;
  name: string;
  description: string;
  articleCount: number;
}

interface Breadcrumb {
  label: string;
  href?: string;
  current: boolean;
}

// Enhanced categories with subcategories and better organization
const HELP_CATEGORIES: HelpCategory[] = [
  {
    id: 'getting-started',
    name: 'Getting Started',
    description: 'Learn the basics and get up and running quickly',
    icon: BookOpenIcon,
    articleCount: 12,
    featured: true,
    color: 'blue',
    subcategories: [
      { id: 'quick-start', name: 'Quick Start Guide', description: '5-minute setup', articleCount: 3 },
      { id: 'first-steps', name: 'First Steps', description: 'Essential tasks', articleCount: 4 },
      { id: 'onboarding', name: 'Onboarding', description: 'Guided tours', articleCount: 5 }
    ]
  },
  {
    id: 'prompts',
    name: 'Creating Prompts',
    description: 'Master the art of prompt engineering',
    icon: LightBulbIcon,
    articleCount: 18,
    featured: true,
    color: 'green',
    subcategories: [
      { id: 'basics', name: 'Prompt Basics', description: 'Fundamentals', articleCount: 6 },
      { id: 'advanced', name: 'Advanced Techniques', description: 'Expert strategies', articleCount: 7 },
      { id: 'templates', name: 'Templates', description: 'Ready-to-use prompts', articleCount: 5 }
    ]
  },
  {
    id: 'documents',
    name: 'Document Management',
    description: 'Upload and manage documents for RAG',
    icon: DocumentTextIcon,
    articleCount: 10,
    featured: false,
    color: 'purple',
    subcategories: [
      { id: 'upload', name: 'Uploading', description: 'File management', articleCount: 4 },
      { id: 'processing', name: 'Processing', description: 'Document analysis', articleCount: 3 },
      { id: 'search', name: 'Search & Retrieval', description: 'Finding content', articleCount: 3 }
    ]
  },
  {
    id: 'api',
    name: 'API & Integrations',
    description: 'Integrate with your existing workflows',
    icon: ChatBubbleLeftRightIcon,
    articleCount: 15,
    featured: false,
    color: 'orange',
    subcategories: [
      { id: 'authentication', name: 'Authentication', description: 'API keys & auth', articleCount: 3 },
      { id: 'endpoints', name: 'Endpoints', description: 'API reference', articleCount: 8 },
      { id: 'sdks', name: 'SDKs', description: 'Client libraries', articleCount: 4 }
    ]
  },
  {
    id: 'troubleshooting',
    name: 'Troubleshooting',
    description: 'Solve common issues and problems',
    icon: ExclamationTriangleIcon,
    articleCount: 8,
    featured: false,
    color: 'red',
    subcategories: [
      { id: 'common-issues', name: 'Common Issues', description: 'Frequent problems', articleCount: 5 },
      { id: 'error-codes', name: 'Error Codes', description: 'Error explanations', articleCount: 3 }
    ]
  },
  {
    id: 'tutorials',
    name: 'Video Tutorials',
    description: 'Step-by-step video guides',
    icon: VideoCameraIcon,
    articleCount: 6,
    featured: true,
    color: 'indigo'
  }
];

// Enhanced articles with rich content and multimedia support
const HELP_ARTICLES: HelpArticle[] = [
  {
    id: 'quick-start-guide',
    title: 'Quick Start Guide',
    excerpt: 'Get up and running with RAG Prompt Library in just 5 minutes',
    content: `# Quick Start Guide

Welcome to RAG Prompt Library! This guide will get you started in just 5 minutes.

## What You'll Learn
- How to create your first account
- Setting up your workspace
- Creating your first prompt
- Running your first AI generation

## Prerequisites
- A modern web browser
- Internet connection
- Email address for account creation`,
    category: 'getting-started',
    subcategory: 'quick-start',
    tags: ['beginner', 'setup', 'tutorial', 'onboarding'],
    difficulty: 'beginner',
    lastUpdated: '2024-01-15',
    views: 1250,
    helpful: 98,
    rating: 4.8,
    estimatedReadTime: 5,
    type: 'tutorial',
    featured: true,
    relatedArticles: ['creating-first-prompt', 'workspace-setup'],
    steps: [
      {
        id: 'step-1',
        title: 'Create Your Account',
        content: 'Sign up using your email or Google account',
        tips: ['Use a business email for team features', 'Enable 2FA for security']
      },
      {
        id: 'step-2',
        title: 'Complete Onboarding',
        content: 'Follow the guided tour to learn the interface',
        tips: ['Take your time with each step', 'You can restart the tour anytime']
      }
    ]
  },
  {
    id: 'creating-first-prompt',
    title: 'Creating Your First Prompt',
    excerpt: 'Learn the fundamentals of prompt creation with variables and best practices',
    content: `# Creating Your First Prompt

Master the art of prompt creation with this comprehensive guide.

## Understanding Prompts
Prompts are reusable templates that generate dynamic content using AI.

## Key Concepts
- Variables: Use {{variable_name}} syntax
- Context: Provide clear instructions
- Examples: Include sample outputs`,
    category: 'prompts',
    subcategory: 'basics',
    tags: ['prompts', 'creation', 'variables', 'templates'],
    difficulty: 'beginner',
    lastUpdated: '2024-01-14',
    views: 890,
    helpful: 87,
    rating: 4.6,
    estimatedReadTime: 8,
    type: 'tutorial',
    featured: true,
    prerequisites: ['quick-start-guide'],
    relatedArticles: ['advanced-prompting', 'prompt-variables'],
    videoUrl: 'https://example.com/video/creating-prompts',
    faqs: [
      {
        id: 'faq-1',
        question: 'What are prompt variables?',
        answer: 'Variables are placeholders in your prompts that get replaced with actual values when executed.',
        helpful: 95
      }
    ]
  },
  {
    id: 'advanced-prompt-engineering',
    title: 'Advanced Prompt Engineering Techniques',
    excerpt: 'Master sophisticated prompting strategies for complex AI tasks',
    content: `# Advanced Prompt Engineering Techniques

## Chain-of-Thought Prompting
Guide the AI through step-by-step reasoning to improve accuracy and transparency.

### Example:
\`\`\`
Question: What is 15% of 240?
Let me think step by step:
1. First, I need to convert 15% to a decimal: 15% = 0.15
2. Then multiply: 240 × 0.15 = 36
Therefore, 15% of 240 is 36.
\`\`\`

## Few-Shot Learning
Provide multiple examples to help the model understand patterns.

## Prompt Chaining
Break complex tasks into smaller, manageable steps.

## Context Window Optimization
Efficiently use available context space for better results.`,
    category: 'prompts',
    subcategory: 'advanced',
    tags: ['advanced', 'techniques', 'chain-of-thought', 'few-shot'],
    difficulty: 'advanced',
    lastUpdated: '2024-01-14',
    views: 756,
    helpful: 88,
    rating: 4.6,
    estimatedReadTime: 15,
    type: 'article',
    featured: false,
    prerequisites: ['quick-start-guide', 'creating-first-prompt'],
    relatedArticles: ['creating-first-prompt', 'api-integration-guide']
  },
  {
    id: 'api-integration-guide',
    title: 'API Integration Guide',
    excerpt: 'Complete guide to integrating RAG Prompt Library with your applications',
    content: `# API Integration Guide

## Authentication
All API requests require authentication using your API key.

\`\`\`javascript
const headers = {
  'Authorization': 'Bearer YOUR_API_KEY',
  'Content-Type': 'application/json'
};
\`\`\`

## Core Endpoints

### POST /api/prompts/execute
Execute a prompt with given parameters.

### GET /api/prompts
List all available prompts.

### POST /api/documents/upload
Upload documents to your knowledge base.

## Error Handling
Implement proper error handling for robust applications.

## Rate Limiting
Understand and respect API rate limits.`,
    category: 'api',
    subcategory: 'endpoints',
    tags: ['api', 'integration', 'authentication', 'endpoints'],
    difficulty: 'intermediate',
    lastUpdated: '2024-01-13',
    views: 1100,
    helpful: 94,
    rating: 4.8,
    estimatedReadTime: 12,
    type: 'guide',
    featured: true,
    prerequisites: ['quick-start-guide'],
    relatedArticles: ['troubleshooting-guide', 'security-best-practices']
  },
  {
    id: 'troubleshooting-guide',
    title: 'Troubleshooting Common Issues',
    excerpt: 'Solutions to frequently encountered problems and error messages',
    content: `# Troubleshooting Common Issues

## Authentication Errors

### Error: "Invalid API Key"
**Cause**: Your API key is incorrect or expired.
**Solution**:
1. Check your API key in the dashboard
2. Regenerate if necessary
3. Update your environment variables

## Performance Issues

### Slow Response Times
**Causes**: Large documents, complex prompts, or network issues.
**Solutions**:
- Optimize document chunking
- Simplify prompts
- Check network connectivity

## Document Upload Problems

### Error: "File format not supported"
**Supported formats**: PDF, DOCX, TXT, MD
**Solution**: Convert your file to a supported format.

## Getting Additional Help
- Check our FAQ section
- Join our community Discord
- Contact support team`,
    category: 'troubleshooting',
    subcategory: 'common-issues',
    tags: ['troubleshooting', 'errors', 'debugging', 'support'],
    difficulty: 'beginner',
    lastUpdated: '2024-01-12',
    views: 890,
    helpful: 87,
    rating: 4.4,
    estimatedReadTime: 8,
    type: 'troubleshooting',
    featured: false,
    prerequisites: [],
    relatedArticles: ['api-integration-guide', 'security-best-practices'],
    faqs: [
      {
        id: 'auth-error',
        question: 'Why am I getting authentication errors?',
        answer: 'Check that your API key is correctly set and has not expired. You can regenerate it in your dashboard.',
        helpful: 78,
        category: 'authentication'
      },
      {
        id: 'slow-performance',
        question: 'Why are my prompts running slowly?',
        answer: 'Large documents or complex prompts can slow performance. Try optimizing your content and simplifying prompts.',
        helpful: 65,
        category: 'performance'
      }
    ]
  }
];

// Enhanced popular searches with categories
const POPULAR_SEARCHES = [
  { query: 'How to create a prompt', category: 'prompts' },
  { query: 'Upload documents', category: 'documents' },
  { query: 'API authentication', category: 'api' },
  { query: 'Variable syntax', category: 'prompts' },
  { query: 'Troubleshooting errors', category: 'troubleshooting' },
  { query: 'Getting started guide', category: 'getting-started' },
  { query: 'Video tutorials', category: 'tutorials' },
  { query: 'Best practices', category: 'prompts' }
];

// Search suggestions for autocomplete
const SEARCH_SUGGESTIONS = [
  'create prompt', 'upload document', 'API key', 'variables', 'troubleshoot',
  'getting started', 'tutorial', 'guide', 'FAQ', 'error', 'authentication',
  'integration', 'best practices', 'examples', 'templates'
];

export const HelpCenter: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);
  const [filteredArticles, setFilteredArticles] = useState<HelpArticle[]>(HELP_ARTICLES);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    difficulty: [],
    type: [],
    category: [],
    tags: [],
    dateRange: 'all',
    sortBy: 'relevance'
  });
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [breadcrumbs, setBreadcrumbs] = useState<Breadcrumb[]>([
    { label: 'Help Center', current: true }
  ]);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Analytics and feedback state
  const [analytics, setAnalytics] = useState({
    sessionStartTime: Date.now(),
    searchQueries: [] as string[],
    articlesViewed: [] as string[],
    categoriesExplored: [] as string[],
    feedbackSubmitted: [] as { articleId: string; helpful: boolean; timestamp: number }[]
  });

  // Enhanced filtering with multiple criteria
  useEffect(() => {
    let filtered = HELP_ARTICLES;

    // Category filter
    if (selectedCategory) {
      filtered = filtered.filter(article => article.category === selectedCategory);
    }

    // Subcategory filter
    if (selectedSubcategory) {
      filtered = filtered.filter(article => article.subcategory === selectedSubcategory);
    }

    // Search query filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(query) ||
        article.excerpt?.toLowerCase().includes(query) ||
        article.content.toLowerCase().includes(query) ||
        article.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Advanced filters
    if (filters.difficulty.length > 0) {
      filtered = filtered.filter(article => filters.difficulty.includes(article.difficulty));
    }

    if (filters.type.length > 0) {
      filtered = filtered.filter(article => filters.type.includes(article.type));
    }

    if (filters.tags.length > 0) {
      filtered = filtered.filter(article =>
        filters.tags.some(tag => article.tags.includes(tag))
      );
    }

    // Sort results
    switch (filters.sortBy) {
      case 'date':
        filtered.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());
        break;
      case 'popularity':
        filtered.sort((a, b) => b.views - a.views);
        break;
      case 'rating':
        filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      default: // relevance
        if (searchQuery.trim()) {
          // Simple relevance scoring based on title match
          filtered.sort((a, b) => {
            const aScore = a.title.toLowerCase().includes(searchQuery.toLowerCase()) ? 1 : 0;
            const bScore = b.title.toLowerCase().includes(searchQuery.toLowerCase()) ? 1 : 0;
            return bScore - aScore;
          });
        }
    }

    setFilteredArticles(filtered);
  }, [searchQuery, selectedCategory, selectedSubcategory, filters]);

  // Search suggestions
  useEffect(() => {
    if (searchQuery.trim().length > 1) {
      const suggestions = SEARCH_SUGGESTIONS.filter(suggestion =>
        suggestion.toLowerCase().includes(searchQuery.toLowerCase())
      ).slice(0, 5);
      setSearchSuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
    } else {
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  // Enhanced handlers
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setSelectedCategory(null);
    setSelectedSubcategory(null);
    setSelectedArticle(null);
    setShowSuggestions(false);

    // Track search analytics
    trackEvent('search', { query });

    // Track search performance after filtering
    setTimeout(() => {
      const results = HELP_ARTICLES.filter(article =>
        article.title.toLowerCase().includes(query.toLowerCase()) ||
        article.content.toLowerCase().includes(query.toLowerCase()) ||
        article.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      );
      trackSearchPerformance(query, results.length);
    }, 100);

    updateBreadcrumbs([
      { label: 'Help Center', current: false },
      { label: `Search: "${query}"`, current: true }
    ]);
  }, [trackEvent, trackSearchPerformance]);

  const handleCategorySelect = useCallback((categoryId: string) => {
    const category = HELP_CATEGORIES.find(cat => cat.id === categoryId);
    setSelectedCategory(categoryId);
    setSelectedSubcategory(null);
    setSearchQuery('');
    setSelectedArticle(null);

    // Track category exploration
    trackEvent('category_explore', { categoryId, categoryName: category?.name });
    trackUserEngagement('category_select', { categoryId, categoryName: category?.name });

    updateBreadcrumbs([
      { label: 'Help Center', current: false },
      { label: category?.name || categoryId, current: true }
    ]);
  }, [trackEvent, trackUserEngagement]);

  const handleSubcategorySelect = useCallback((subcategoryId: string) => {
    const category = HELP_CATEGORIES.find(cat => cat.id === selectedCategory);
    const subcategory = category?.subcategories?.find(sub => sub.id === subcategoryId);
    setSelectedSubcategory(subcategoryId);
    setSelectedArticle(null);
    updateBreadcrumbs([
      { label: 'Help Center', current: false },
      { label: category?.name || '', current: false },
      { label: subcategory?.name || subcategoryId, current: true }
    ]);
  }, [selectedCategory]);

  const handleArticleSelect = useCallback((article: HelpArticle) => {
    setSelectedArticle(article);
    // Track article view (in real app, this would be an API call)
    article.views += 1;

    // Track article view analytics
    trackEvent('article_view', {
      articleId: article.id,
      articleTitle: article.title,
      category: article.category,
      subcategory: article.subcategory,
      type: article.type,
      difficulty: article.difficulty
    });
    trackUserEngagement('article_open', {
      articleId: article.id,
      fromSearch: !!searchQuery,
      fromCategory: !!selectedCategory
    });

    const category = HELP_CATEGORIES.find(cat => cat.id === article.category);
    const subcategory = category?.subcategories?.find(sub => sub.id === article.subcategory);

    const breadcrumbPath = [
      { label: 'Help Center', current: false },
      { label: category?.name || article.category, current: false }
    ];

    if (subcategory) {
      breadcrumbPath.push({ label: subcategory.name, current: false });
    }

    breadcrumbPath.push({ label: article.title, current: true });
    updateBreadcrumbs(breadcrumbPath);
  }, [searchQuery, selectedCategory, trackEvent, trackUserEngagement]);

  const updateBreadcrumbs = (newBreadcrumbs: Breadcrumb[]) => {
    setBreadcrumbs(newBreadcrumbs);
  };

  const resetFilters = () => {
    setFilters({
      difficulty: [],
      type: [],
      category: [],
      tags: [],
      dateRange: 'all',
      sortBy: 'relevance'
    });
  };

  // Analytics tracking functions
  const trackEvent = useCallback((eventType: string, data: any) => {
    // In a real application, this would send data to an analytics service
    console.log('Analytics Event:', eventType, data);

    // Update local analytics state for demo purposes
    setAnalytics(prev => {
      switch (eventType) {
        case 'search':
          return {
            ...prev,
            searchQueries: [...prev.searchQueries, data.query]
          };
        case 'article_view':
          return {
            ...prev,
            articlesViewed: [...prev.articlesViewed, data.articleId]
          };
        case 'category_explore':
          return {
            ...prev,
            categoriesExplored: [...prev.categoriesExplored, data.categoryId]
          };
        case 'feedback':
          return {
            ...prev,
            feedbackSubmitted: [...prev.feedbackSubmitted, {
              articleId: data.articleId,
              helpful: data.helpful,
              timestamp: Date.now()
            }]
          };
        default:
          return prev;
      }
    });
  }, []);

  const trackSearchPerformance = useCallback((query: string, resultCount: number) => {
    trackEvent('search_performance', {
      query,
      resultCount,
      timestamp: Date.now(),
      sessionDuration: Date.now() - analytics.sessionStartTime
    });
  }, [analytics.sessionStartTime, trackEvent]);

  const trackUserEngagement = useCallback((action: string, context: any) => {
    trackEvent('user_engagement', {
      action,
      context,
      timestamp: Date.now(),
      sessionDuration: Date.now() - analytics.sessionStartTime
    });
  }, [analytics.sessionStartTime, trackEvent]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <VideoCameraIcon className="h-4 w-4" />;
      case 'tutorial': return <BookOpenIcon className="h-4 w-4" />;
      default: return <QuestionMarkCircleIcon className="h-4 w-4" />;
    }
  };

  if (selectedArticle) {
    return (
      <EnhancedArticleView
        article={selectedArticle}
        onBack={() => setSelectedArticle(null)}
        breadcrumbs={breadcrumbs}
        onFeedback={(articleId: string, helpful: boolean) => {
          trackEvent('feedback', { articleId, helpful });
          trackUserEngagement('feedback_submit', { articleId, helpful });
        }}
      />
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-4 sm:p-6">
      {/* Skip to main content link for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Breadcrumbs */}
      <BreadcrumbNavigation breadcrumbs={breadcrumbs} />

      {/* Header */}
      <header className="text-center mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Help Center</h1>
        <p className="text-gray-600 max-w-2xl mx-auto text-sm sm:text-base">
          Find answers to your questions, learn new features, and get the most out of RAG Prompt Library
        </p>
      </header>

      {/* Enhanced Search Bar with Accessibility and Mobile Optimization */}
      <div className="relative mb-6 sm:mb-8" role="search">
        <label htmlFor="help-search" className="sr-only">
          Search help articles
        </label>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
        </div>
        <input
          id="help-search"
          ref={searchInputRef}
          type="search"
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            if (e.target.value.trim()) {
              setShowSuggestions(true);
            }
          }}
          onFocus={() => searchQuery.trim() && setShowSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && searchQuery.trim()) {
              handleSearch(searchQuery);
            }
            if (e.key === 'Escape') {
              setShowSuggestions(false);
              searchInputRef.current?.blur();
            }
          }}
          placeholder="Search for help articles, tutorials, and guides..."
          className="block w-full pl-10 pr-12 py-3 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          aria-describedby={showSuggestions ? "search-suggestions" : undefined}
          aria-expanded={showSuggestions}
          aria-autocomplete="list"
          autoComplete="off"
        />

        {/* Accessible search suggestions dropdown */}
        {showSuggestions && searchSuggestions.length > 0 && (
          <div
            id="search-suggestions"
            className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto"
            role="listbox"
            aria-label="Search suggestions"
          >
            {searchSuggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSearch(suggestion)}
                className="w-full text-left px-4 py-3 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none first:rounded-t-lg last:rounded-b-lg transition-colors"
                role="option"
                aria-selected="false"
                tabIndex={0}
              >
                <div className="flex items-center">
                  <MagnifyingGlassIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" aria-hidden="true" />
                  <span className="text-gray-900 text-sm sm:text-base">{suggestion}</span>
                </div>
              </button>
            ))}
          </div>
        )}

        {/* Advanced filters toggle with accessibility */}
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="absolute inset-y-0 right-0 pr-3 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md"
          aria-label={showFilters ? 'Hide advanced filters' : 'Show advanced filters'}
          aria-expanded={showFilters}
          aria-controls="advanced-filters"
        >
          <AdjustmentsHorizontalIcon
            className={`h-5 w-5 transition-colors ${showFilters ? 'text-blue-600' : 'text-gray-400'}`}
            aria-hidden="true"
          />
        </button>
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <AdvancedFiltersPanel
          filters={filters}
          setFilters={setFilters}
          onReset={resetFilters}
          onClose={() => setShowFilters(false)}
        />
      )}

      {/* Popular Searches and Quick Actions */}
      {!searchQuery && !selectedCategory && (
        <div className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Popular Searches */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Popular searches:</h3>
              <div className="flex flex-wrap gap-2">
                {POPULAR_SEARCHES.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearch(search.query)}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors flex items-center"
                  >
                    <span>{search.query}</span>
                    {search.category && (
                      <span className="ml-1 text-xs text-gray-500">
                        in {HELP_CATEGORIES.find(cat => cat.id === search.category)?.name}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Quick actions:</h3>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => handleCategorySelect('getting-started')}
                  className="p-3 text-left bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <BookOpenIcon className="h-5 w-5 text-blue-600 mb-1" />
                  <div className="text-sm font-medium text-blue-900">Getting Started</div>
                  <div className="text-xs text-blue-700">New to the platform?</div>
                </button>
                <button
                  onClick={() => handleCategorySelect('tutorials')}
                  className="p-3 text-left bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
                >
                  <VideoCameraIcon className="h-5 w-5 text-green-600 mb-1" />
                  <div className="text-sm font-medium text-green-900">Video Tutorials</div>
                  <div className="text-xs text-green-700">Learn by watching</div>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Featured Articles */}
      {!searchQuery && !selectedCategory && (
        <FeaturedArticlesSection articles={HELP_ARTICLES.filter(a => a.featured)} onArticleSelect={handleArticleSelect} />
      )}

      {/* Main content area with improved mobile layout */}
      <main id="main-content" className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
        {/* Enhanced Categories Sidebar with Mobile Optimization */}
        <aside className="lg:col-span-1 order-2 lg:order-1" aria-label="Help categories">
          {/* Mobile category toggle */}
          <div className="lg:hidden mb-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="w-full flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-expanded={showFilters}
              aria-controls="mobile-categories"
            >
              <span className="font-medium text-gray-900">Browse Categories</span>
              <ChevronDownIcon
                className={`h-5 w-5 text-gray-500 transition-transform ${showFilters ? 'rotate-180' : ''}`}
                aria-hidden="true"
              />
            </button>
          </div>

          <div
            id="mobile-categories"
            className={`${showFilters || 'lg:block'} ${showFilters ? 'block' : 'hidden lg:block'}`}
          >
            <h3 className="text-lg font-semibold mb-4">Categories</h3>
            <nav className="space-y-2" role="navigation" aria-label="Help categories">
              <button
                onClick={() => {
                  setSelectedCategory(null);
                  setSelectedSubcategory(null);
                  setSearchQuery('');
                  updateBreadcrumbs([{ label: 'Help Center', current: true }]);
                }}
                className={`w-full text-left p-3 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  !selectedCategory ? 'bg-blue-50 text-blue-700 border border-blue-200' : 'hover:bg-gray-50'
                }`}
                aria-current={!selectedCategory ? 'page' : undefined}
              >
                All Articles ({HELP_ARTICLES.length})
              </button>
            {HELP_CATEGORIES.map((category) => {
              const IconComponent = category.icon;
              const isSelected = selectedCategory === category.id;
              return (
                <div key={category.id}>
                  <button
                    onClick={() => handleCategorySelect(category.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isSelected
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'hover:bg-gray-50'
                    }`}
                    aria-current={isSelected ? 'page' : undefined}
                    aria-expanded={isSelected && category.subcategories && category.subcategories.length > 0}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <IconComponent className="h-5 w-5 mr-3" />
                        <div>
                          <div className="font-medium">{category.name}</div>
                          <div className="text-sm text-gray-500">
                            {category.articleCount} articles
                          </div>
                        </div>
                      </div>
                      {category.subcategories && category.subcategories.length > 0 && (
                        <ChevronDownIcon
                          className={`h-4 w-4 transition-transform ${
                            isSelected ? 'rotate-180' : ''
                          }`}
                        />
                      )}
                    </div>
                  </button>

                  {/* Subcategories */}
                  {isSelected && category.subcategories && category.subcategories.length > 0 && (
                    <div className="ml-4 mt-2 space-y-1">
                      {category.subcategories.map((subcategory) => (
                        <button
                          key={subcategory.id}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSubcategorySelect(subcategory.id);
                          }}
                          className={`w-full text-left p-2 rounded-md text-sm transition-colors ${
                            selectedSubcategory === subcategory.id
                              ? 'bg-blue-100 text-blue-800'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <div className="font-medium">{subcategory.name}</div>
                          <div className="text-xs text-gray-500">
                            {subcategory.articleCount} articles
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Enhanced Articles List with Mobile Optimization */}
        <section className="lg:col-span-3 order-1 lg:order-2" aria-label="Help articles">
          {/* Search Results Header */}
          {searchQuery && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-900 font-medium">
                    Found {filteredArticles.length} result{filteredArticles.length !== 1 ? 's' : ''} for "{searchQuery}"
                  </p>
                  {filteredArticles.length > 0 && (
                    <p className="text-sm text-blue-700 mt-1">
                      Sorted by {filters.sortBy === 'relevance' ? 'relevance' : filters.sortBy}
                    </p>
                  )}
                </div>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setShowSuggestions(false);
                  }}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Clear search
                </button>
              </div>
            </div>
          )}

          {/* Category/Subcategory Header */}
          {(selectedCategory || selectedSubcategory) && !searchQuery && (
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    {selectedSubcategory
                      ? HELP_CATEGORIES.find(cat => cat.id === selectedCategory)?.subcategories?.find(sub => sub.id === selectedSubcategory)?.name
                      : HELP_CATEGORIES.find(cat => cat.id === selectedCategory)?.name
                    }
                  </h2>
                  <p className="text-gray-600 mt-1">
                    {selectedSubcategory
                      ? HELP_CATEGORIES.find(cat => cat.id === selectedCategory)?.subcategories?.find(sub => sub.id === selectedSubcategory)?.description
                      : HELP_CATEGORIES.find(cat => cat.id === selectedCategory)?.description
                    }
                  </p>
                </div>
                <span className="text-sm text-gray-500">
                  {filteredArticles.length} articles
                </span>
              </div>
            </div>
          )}

          {/* Articles Grid */}
          <div className="space-y-4">
            {filteredArticles.map((article) => (
              <EnhancedArticleCard
                key={article.id}
                article={article}
                onSelect={handleArticleSelect}
                searchQuery={searchQuery}
              />
            ))}
          </div>

          {/* Empty State */}
          {filteredArticles.length === 0 && (
            <EmptyState
              searchQuery={searchQuery}
              selectedCategory={selectedCategory}
              onClearSearch={() => setSearchQuery('')}
              onResetFilters={resetFilters}
            />
          )}

          {/* Load More (for future pagination) */}
          {filteredArticles.length > 0 && filteredArticles.length >= 10 && (
            <div className="text-center mt-8">
              <button className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                Load more articles
              </button>
            </div>
          )}
        </section>
          </div>
      </main>
    </div>
  );
};

// Article View Component
const ArticleView: React.FC<{
  article: HelpArticle;
  onBack: () => void;
}> = ({ article, onBack }) => {
  const [isHelpful, setIsHelpful] = useState<boolean | null>(null);

  const handleHelpfulVote = (helpful: boolean) => {
    setIsHelpful(helpful);
    // In a real app, this would send the vote to the backend
    if (helpful) {
      article.helpful += 1;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <button
        onClick={onBack}
        className="mb-6 text-blue-600 hover:text-blue-800 flex items-center"
      >
        ← Back to Help Center
      </button>

      <article className="bg-white rounded-lg border border-gray-200 p-8">
        <header className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{article.title}</h1>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(article.difficulty)}`}>
              {article.difficulty}
            </span>
            <span>{article.views} views</span>
            <span>Updated {new Date(article.lastUpdated).toLocaleDateString()}</span>
          </div>
        </header>

        <div className="prose max-w-none mb-8">
          {/* In a real app, this would render markdown or rich text */}
          <p>{article.content}</p>
        </div>

        <footer className="border-t border-gray-200 pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium mb-2">Was this article helpful?</h4>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleHelpfulVote(true)}
                  className={`px-4 py-2 rounded ${
                    isHelpful === true
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  👍 Yes
                </button>
                <button
                  onClick={() => handleHelpfulVote(false)}
                  className={`px-4 py-2 rounded ${
                    isHelpful === false
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  👎 No
                </button>
              </div>
            </div>
            
            <div className="text-right">
              <p className="text-sm text-gray-500 mb-2">Still need help?</p>
              <button className="text-blue-600 hover:text-blue-800 text-sm">
                Contact Support
              </button>
            </div>
          </div>
        </footer>
      </article>
    </div>
  );
};

function getDifficultyColor(difficulty: string) {
  switch (difficulty) {
    case 'beginner': return 'bg-green-100 text-green-800';
    case 'intermediate': return 'bg-yellow-100 text-yellow-800';
    case 'advanced': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}

// Enhanced Rich Content Components

// Breadcrumb Navigation Component
const BreadcrumbNavigation: React.FC<{ breadcrumbs: Breadcrumb[] }> = ({ breadcrumbs }) => {
  return (
    <nav className="flex mb-6" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && <ChevronRightIcon className="h-4 w-4 text-gray-400 mx-2" />}
            {breadcrumb.current ? (
              <span className="text-gray-500 text-sm">{breadcrumb.label}</span>
            ) : (
              <button
                onClick={() => {
                  // Navigate back to previous level
                  if (index === 0) {
                    window.location.reload(); // Reset to home
                  }
                }}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                {breadcrumb.label}
              </button>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Advanced Filters Panel Component
const AdvancedFiltersPanel: React.FC<{
  filters: SearchFilters;
  setFilters: (filters: SearchFilters) => void;
  onReset: () => void;
  onClose: () => void;
}> = ({ filters, setFilters, onReset, onClose }) => {
  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters({ ...filters, [key]: value });
  };

  const toggleArrayFilter = (key: 'difficulty' | 'type' | 'category' | 'tags', value: string) => {
    const currentArray = filters[key] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFilter(key, newArray);
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Advanced Filters</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={onReset}
            className="text-sm text-gray-600 hover:text-gray-800"
          >
            Reset
          </button>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Difficulty Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
          <div className="space-y-2">
            {['beginner', 'intermediate', 'advanced'].map(difficulty => (
              <label key={difficulty} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.difficulty.includes(difficulty)}
                  onChange={() => toggleArrayFilter('difficulty', difficulty)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700 capitalize">{difficulty}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Content Type Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
          <div className="space-y-2">
            {['article', 'tutorial', 'video', 'guide', 'faq', 'troubleshooting'].map(type => (
              <label key={type} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.type.includes(type)}
                  onChange={() => toggleArrayFilter('type', type)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700 capitalize">{type}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Sort By */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
          <select
            value={filters.sortBy}
            onChange={(e) => updateFilter('sortBy', e.target.value)}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="relevance">Relevance</option>
            <option value="date">Most Recent</option>
            <option value="popularity">Most Popular</option>
            <option value="rating">Highest Rated</option>
          </select>
        </div>

        {/* Date Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Updated</label>
          <select
            value={filters.dateRange}
            onChange={(e) => updateFilter('dateRange', e.target.value)}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="all">All Time</option>
            <option value="week">Past Week</option>
            <option value="month">Past Month</option>
            <option value="quarter">Past 3 Months</option>
          </select>
        </div>
      </div>
    </div>
  );
};

// Featured Articles Section Component
const FeaturedArticlesSection: React.FC<{
  articles: HelpArticle[];
  onArticleSelect: (article: HelpArticle) => void;
}> = ({ articles, onArticleSelect }) => {
  if (articles.length === 0) return null;

  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Featured Articles</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {articles.slice(0, 6).map(article => (
          <div
            key={article.id}
            onClick={() => onArticleSelect(article)}
            className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer group"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center">
                {getTypeIcon(article.type)}
                <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getDifficultyColor(article.difficulty)}`}>
                  {article.difficulty}
                </span>
              </div>
              {article.rating && (
                <div className="flex items-center">
                  <StarIconSolid className="h-4 w-4 text-yellow-400" />
                  <span className="ml-1 text-sm text-gray-600">{article.rating}</span>
                </div>
              )}
            </div>

            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
              {article.title}
            </h3>

            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {article.excerpt || article.content.substring(0, 120) + '...'}
            </p>

            <div className="flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center space-x-3">
                <span className="flex items-center">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  {article.estimatedReadTime}m read
                </span>
                <span className="flex items-center">
                  <EyeIcon className="h-3 w-3 mr-1" />
                  {article.views}
                </span>
              </div>
              <span>Updated {new Date(article.lastUpdated).toLocaleDateString()}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Enhanced Article View Component with Rich Content and Analytics
const EnhancedArticleView: React.FC<{
  article: HelpArticle;
  onBack: () => void;
  breadcrumbs: Breadcrumb[];
  onFeedback?: (articleId: string, helpful: boolean) => void;
}> = ({ article, onBack, breadcrumbs, onFeedback }) => {
  const [isHelpful, setIsHelpful] = useState<boolean | null>(null);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  const handleFeedback = (helpful: boolean) => {
    setIsHelpful(helpful);
    setFeedbackSubmitted(true);

    // Track feedback
    if (onFeedback) {
      onFeedback(article.id, helpful);
    }

    // Update article helpful percentage (in real app, this would be an API call)
    if (helpful) {
      article.helpful = Math.min(100, article.helpful + 1);
    }
  };
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [showTableOfContents, setShowTableOfContents] = useState(false);

  const handleHelpfulVote = (helpful: boolean) => {
    setIsHelpful(helpful);
    // In a real app, this would send the vote to the backend
    if (helpful) {
      article.helpful += 1;
    }
  };

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  return (
    <div className="max-w-5xl mx-auto p-6">
      {/* Breadcrumbs */}
      <BreadcrumbNavigation breadcrumbs={breadcrumbs} />

      {/* Back Button */}
      <button
        onClick={onBack}
        className="mb-6 text-blue-600 hover:text-blue-800 flex items-center"
      >
        ← Back to Help Center
      </button>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Table of Contents (for longer articles) */}
        {article.steps && article.steps.length > 0 && (
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Table of Contents</h3>
              <nav className="space-y-1">
                {article.steps.map((step, index) => (
                  <button
                    key={step.id}
                    onClick={() => setCurrentStep(index)}
                    className={`block w-full text-left text-sm px-3 py-2 rounded ${
                      currentStep === index
                        ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    {index + 1}. {step.title}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className={`${article.steps && article.steps.length > 0 ? 'lg:col-span-3' : 'lg:col-span-4'}`}>
          <article className="bg-white rounded-lg border border-gray-200 p-8">
            {/* Article Header */}
            <header className="mb-8">
              <div className="flex items-center mb-4">
                {getTypeIcon(article.type)}
                <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getDifficultyColor(article.difficulty)}`}>
                  {article.difficulty}
                </span>
                {article.featured && (
                  <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                    Featured
                  </span>
                )}
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{article.title}</h1>

              {article.excerpt && (
                <p className="text-lg text-gray-600 mb-4">{article.excerpt}</p>
              )}

              <div className="flex items-center space-x-6 text-sm text-gray-500 mb-6">
                <span className="flex items-center">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {article.estimatedReadTime} min read
                </span>
                <span className="flex items-center">
                  <EyeIcon className="h-4 w-4 mr-1" />
                  {article.views} views
                </span>
                {article.rating && (
                  <span className="flex items-center">
                    <StarIconSolid className="h-4 w-4 text-yellow-400 mr-1" />
                    {article.rating} rating
                  </span>
                )}
                <span>Updated {new Date(article.lastUpdated).toLocaleDateString()}</span>
              </div>

              {/* Prerequisites */}
              {article.prerequisites && article.prerequisites.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-blue-900 mb-2">Prerequisites</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    {article.prerequisites.map((prereq, index) => (
                      <li key={index}>• {prereq}</li>
                    ))}
                  </ul>
                </div>
              )}
            </header>

            {/* Video Content */}
            {article.videoUrl && (
              <div className="mb-8">
                <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <PlayIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Video content would be embedded here</p>
                    <p className="text-sm text-gray-500 mt-2">{article.videoUrl}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Step-by-Step Content */}
            {article.steps && article.steps.length > 0 ? (
              <StepByStepGuide
                steps={article.steps}
                currentStep={currentStep}
                setCurrentStep={setCurrentStep}
              />
            ) : (
              /* Regular Content */
              <div className="prose max-w-none mb-8">
                <div className="whitespace-pre-wrap">{article.content}</div>
              </div>
            )}

            {/* FAQ Section */}
            {article.faqs && article.faqs.length > 0 && (
              <FAQSection
                faqs={article.faqs}
                expandedFAQ={expandedFAQ}
                toggleFAQ={toggleFAQ}
              />
            )}
          </article>

          {/* Related Articles */}
          {article.relatedArticles && article.relatedArticles.length > 0 && (
            <RelatedArticlesSection
              relatedIds={article.relatedArticles}
              currentArticleId={article.id}
            />
          )}

          {/* Enhanced Feedback Section with Analytics */}
          <FeedbackSection
            article={article}
            isHelpful={isHelpful}
            onHelpfulVote={handleFeedback}
            feedbackSubmitted={feedbackSubmitted}
          />
        </div>
      </div>
    </div>
  );
};

// Step-by-Step Guide Component
const StepByStepGuide: React.FC<{
  steps: ArticleStep[];
  currentStep: number;
  setCurrentStep: (step: number) => void;
}> = ({ steps, currentStep, setCurrentStep }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
        </h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
            className="p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronLeftIcon className="h-4 w-4" />
          </button>
          <span className="text-sm text-gray-500">
            {currentStep + 1} / {steps.length}
          </span>
          <button
            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}
            disabled={currentStep === steps.length - 1}
            className="p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronRightIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Current Step Content */}
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="prose max-w-none mb-4">
          <div className="whitespace-pre-wrap">{steps[currentStep].content}</div>
        </div>

        {/* Code Block */}
        {steps[currentStep].code && (
          <div className="mb-4">
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <code>{steps[currentStep].code}</code>
            </pre>
          </div>
        )}

        {/* Tips */}
        {steps[currentStep].tips && steps[currentStep].tips!.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2 flex items-center">
              <LightBulbIcon className="h-4 w-4 mr-2" />
              Tips
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              {steps[currentStep].tips!.map((tip, index) => (
                <li key={index}>• {tip}</li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Step Navigation */}
      <div className="flex justify-between mt-6">
        <button
          onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
          disabled={currentStep === 0}
          className="flex items-center px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          <ChevronLeftIcon className="h-4 w-4 mr-2" />
          Previous
        </button>
        <button
          onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}
          disabled={currentStep === steps.length - 1}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-700"
        >
          Next
          <ChevronRightIcon className="h-4 w-4 ml-2" />
        </button>
      </div>
    </div>
  );
};

// FAQ Section Component
const FAQSection: React.FC<{
  faqs: FAQ[];
  expandedFAQ: string | null;
  toggleFAQ: (faqId: string) => void;
}> = ({ faqs, expandedFAQ, toggleFAQ }) => {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Frequently Asked Questions</h3>
      <div className="space-y-3">
        {faqs.map(faq => (
          <div key={faq.id} className="border border-gray-200 rounded-lg">
            <button
              onClick={() => toggleFAQ(faq.id)}
              className="w-full text-left p-4 hover:bg-gray-50 flex items-center justify-between"
            >
              <span className="font-medium text-gray-900">{faq.question}</span>
              <ChevronDownIcon
                className={`h-5 w-5 text-gray-500 transition-transform ${
                  expandedFAQ === faq.id ? 'rotate-180' : ''
                }`}
              />
            </button>
            {expandedFAQ === faq.id && (
              <div className="px-4 pb-4">
                <div className="text-gray-700 mb-3">{faq.answer}</div>
                <div className="flex items-center text-sm text-gray-500">
                  <span>Was this helpful?</span>
                  <button className="ml-2 text-green-600 hover:text-green-800">
                    👍 {faq.helpful}
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Related Articles Section Component
const RelatedArticlesSection: React.FC<{
  relatedIds: string[];
  currentArticleId: string;
}> = ({ relatedIds, currentArticleId }) => {
  const relatedArticles = HELP_ARTICLES.filter(article =>
    relatedIds.includes(article.id) && article.id !== currentArticleId
  );

  if (relatedArticles.length === 0) return null;

  return (
    <div className="mt-8 bg-gray-50 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Articles</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {relatedArticles.map(article => (
          <div
            key={article.id}
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
          >
            <div className="flex items-center mb-2">
              {getTypeIcon(article.type)}
              <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getDifficultyColor(article.difficulty)}`}>
                {article.difficulty}
              </span>
            </div>
            <h4 className="font-medium text-gray-900 mb-2">{article.title}</h4>
            <p className="text-sm text-gray-600 line-clamp-2">
              {article.excerpt || article.content.substring(0, 100) + '...'}
            </p>
            <div className="flex items-center mt-3 text-xs text-gray-500">
              <ClockIcon className="h-3 w-3 mr-1" />
              {article.estimatedReadTime}m read
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Enhanced Feedback Section Component with Analytics
const FeedbackSection: React.FC<{
  article: HelpArticle;
  isHelpful: boolean | null;
  onHelpfulVote: (helpful: boolean) => void;
  feedbackSubmitted?: boolean;
}> = ({ article, isHelpful, onHelpfulVote, feedbackSubmitted }) => {
  const [showDetailedFeedback, setShowDetailedFeedback] = useState(false);
  const [detailedFeedback, setDetailedFeedback] = useState('');
  return (
    <div className="mt-8 bg-gray-50 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Was this article helpful?</h3>
      <div className="flex items-center space-x-4 mb-4">
        <button
          onClick={() => onHelpfulVote(true)}
          className={`flex items-center px-4 py-2 rounded-md border transition-colors ${
            isHelpful === true
              ? 'bg-green-50 border-green-300 text-green-700'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          <HandThumbUpIcon className="h-4 w-4 mr-2" />
          Yes ({article.helpful})
        </button>
        <button
          onClick={() => onHelpfulVote(false)}
          className={`flex items-center px-4 py-2 rounded-md border transition-colors ${
            isHelpful === false
              ? 'bg-red-50 border-red-300 text-red-700'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          <HandThumbDownIcon className="h-4 w-4 mr-2" />
          No
        </button>
      </div>

      {isHelpful !== null && (
        <div className="text-sm text-gray-600">
          {isHelpful ? 'Thank you for your feedback!' : 'We\'re sorry this wasn\'t helpful. Please let us know how we can improve.'}
        </div>
      )}
    </div>
  );
};

// Utility Functions
const getTypeIcon = (type: string) => {
  const iconClass = "h-5 w-5";
  switch (type) {
    case 'video':
      return <VideoCameraIcon className={`${iconClass} text-red-500`} />;
    case 'tutorial':
      return <AcademicCapIcon className={`${iconClass} text-blue-500`} />;
    case 'guide':
      return <BookOpenIcon className={`${iconClass} text-green-500`} />;
    case 'faq':
      return <QuestionMarkCircleIcon className={`${iconClass} text-purple-500`} />;
    case 'troubleshooting':
      return <ExclamationTriangleIcon className={`${iconClass} text-orange-500`} />;
    default:
      return <DocumentTextIcon className={`${iconClass} text-gray-500`} />;
  }
};

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'beginner':
      return 'bg-green-100 text-green-800';
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-800';
    case 'advanced':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Enhanced Article Card Component
const EnhancedArticleCard: React.FC<{
  article: HelpArticle;
  onSelect: (article: HelpArticle) => void;
  searchQuery?: string;
}> = ({ article, onSelect, searchQuery }) => {
  const highlightText = (text: string, query: string) => {
    if (!query) return text;
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
  };

  return (
    <article
      onClick={() => onSelect(article)}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onSelect(article);
        }
      }}
      className="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 hover:shadow-md focus:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all cursor-pointer group"
      tabIndex={0}
      role="button"
      aria-label={`Read article: ${article.title}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-grow">
          <div className="flex flex-wrap items-center gap-2 mb-3">
            {getTypeIcon(article.type)}
            <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(article.difficulty)}`}>
              {article.difficulty}
            </span>
            {article.featured && (
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                Featured
              </span>
            )}
            {article.videoUrl && (
              <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs flex items-center">
                <PlayIcon className="h-3 w-3 mr-1" aria-hidden="true" />
                Video
              </span>
            )}
          </div>

          <h3
            className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors"
            dangerouslySetInnerHTML={{
              __html: searchQuery ? highlightText(article.title, searchQuery) : article.title
            }}
          />

          <p className="text-gray-600 mb-4 line-clamp-2">
            {article.excerpt || article.content.substring(0, 150) + '...'}
          </p>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500">
              <span className="flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" aria-hidden="true" />
                <span className="sr-only">Estimated read time:</span>
                {article.estimatedReadTime}m read
              </span>
              <span className="flex items-center">
                <EyeIcon className="h-3 w-3 mr-1" aria-hidden="true" />
                <span className="sr-only">Views:</span>
                {article.views} views
              </span>
              {article.rating && (
                <span className="flex items-center">
                  <StarIconSolid className="h-3 w-3 text-yellow-400 mr-1" aria-hidden="true" />
                  <span className="sr-only">Rating:</span>
                  {article.rating}
                </span>
              )}
            </div>
            <span className="text-xs text-gray-500">
              <span className="sr-only">Last updated:</span>
              Updated {new Date(article.lastUpdated).toLocaleDateString()}
            </span>
          </div>

          {/* Tags */}
          {article.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-3">
              {article.tags.slice(0, 4).map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs"
                >
                  {tag}
                </span>
              ))}
              {article.tags.length > 4 && (
                <span className="text-xs text-gray-500">
                  +{article.tags.length - 4} more
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Empty State Component
const EmptyState: React.FC<{
  searchQuery?: string;
  selectedCategory?: string | null;
  onClearSearch: () => void;
  onResetFilters: () => void;
}> = ({ searchQuery, selectedCategory, onClearSearch, onResetFilters }) => {
  return (
    <div className="text-center py-12">
      <QuestionMarkCircleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">No articles found</h3>

      {searchQuery ? (
        <div>
          <p className="text-gray-600 mb-4">
            No articles match your search for "{searchQuery}"
          </p>
          <div className="space-x-3">
            <button
              onClick={onClearSearch}
              className="text-blue-600 hover:text-blue-800"
            >
              Clear search
            </button>
            <button
              onClick={onResetFilters}
              className="text-blue-600 hover:text-blue-800"
            >
              Reset filters
            </button>
          </div>
        </div>
      ) : selectedCategory ? (
        <div>
          <p className="text-gray-600 mb-4">
            No articles available in this category yet
          </p>
          <button
            onClick={() => window.location.reload()}
            className="text-blue-600 hover:text-blue-800"
          >
            Browse all articles
          </button>
        </div>
      ) : (
        <div>
          <p className="text-gray-600 mb-4">
            Try adjusting your search terms or browse by category
          </p>
          <button
            onClick={onResetFilters}
            className="text-blue-600 hover:text-blue-800"
          >
            Reset all filters
          </button>
        </div>
      )}
    </div>
  );
};

export default HelpCenter;
