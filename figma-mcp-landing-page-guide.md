# Figma MCP Landing Page Integration Guide

## 🎯 Quick Start: Connect to Figma for Landing Page Development

### Prerequisites Checklist
- [ ] Figma desktop app installed and updated
- [ ] Professional, Organization, or Enterprise Figma plan (Dev Mode required)
- [ ] VS Code with Augment Code extension
- [ ] Landing page design file open in Figma

### Step 1: Enable Figma MCP Server

1. **Open Figma Desktop App**
   - Ensure you're using the desktop app (not web version)
   - Open your landing page design file

2. **Enable Dev Mode MCP Server**
   - Click **Figma menu** (upper-left corner)
   - Go to **Preferences** → **Dev Mode**
   - Enable **"Enable Dev Mode MCP Server"**
   - Verify server is running at `http://127.0.0.1:3845/sse`

### Step 2: Verify VS Code Configuration

✅ **Already configured!** Your `.vscode/settings.json` has been updated with:
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "figma-dev-mode",
        "transport": "sse",
        "url": "http://127.0.0.1:3845/sse"
      }
    ]
  }
}
```

### Step 3: Restart and Connect

1. **Restart VS Code** to load the MCP configuration
2. **Keep Figma desktop app open** (server only runs while app is open)
3. **Test connection** using the prompts below

## 🚀 Landing Page Development Workflow

### Available Landing Page Components

Your project has these landing page components ready for Figma integration:

- `HeroSection.tsx` - Main hero banner
- `NavigationHeader.tsx` - Navigation menu
- `FeaturesSection.tsx` - Feature highlights
- `BenefitsSection.tsx` - Value propositions
- `TestimonialsSection.tsx` - Customer testimonials
- `CTASection.tsx` - Call-to-action section
- `FooterSection.tsx` - Footer links and info

### Figma MCP Prompts for Landing Page

#### 1. Generate Code from Figma Selection

**Select a frame/component in Figma, then use:**

```
Generate React TypeScript code for my current Figma selection using Tailwind CSS. 
Target this for our landing page components in frontend/src/components/landing/
```

#### 2. Extract Design Tokens

```
Get the color palette, typography, and spacing variables from my Figma selection 
and create CSS custom properties for our landing page design system
```

#### 3. Update Existing Components

```
Update the HeroSection.tsx component to match this Figma design selection. 
Use our existing component structure and Tailwind CSS classes.
```

#### 4. Create New Landing Page Sections

```
Generate a new landing page section component based on this Figma selection.
Create it as a new file in frontend/src/components/landing/ following our naming convention.
```

#### 5. Extract Layout and Spacing

```
Analyze the layout, spacing, and responsive behavior of this Figma selection
and provide Tailwind CSS classes that match the design exactly
```

### Advanced Figma MCP Tools

#### Available MCP Functions:
- **`get_code`** - Generate code (React + Tailwind by default)
- **`get_variable_defs`** - Extract Figma variables and styles
- **`get_code_connect_map`** - Map Figma nodes to existing components
- **`get_image`** - Take screenshots for layout reference

#### Code Connect Integration:
```
Generate code for my Figma selection using components from frontend/src/components/ui
and map to our existing design system components
```

## 🎨 Landing Page Design Best Practices

### Figma File Structure Recommendations

1. **Organize by Sections**
   - Hero Frame
   - Navigation Frame
   - Features Frame
   - Benefits Frame
   - Testimonials Frame
   - CTA Frame
   - Footer Frame

2. **Use Semantic Naming**
   - `HeroSection` instead of `Group 1`
   - `NavigationMenu` instead of `Frame 2`
   - `FeatureCard` instead of `Rectangle 3`

3. **Leverage Figma Variables**
   - Color tokens for brand consistency
   - Typography scale for consistent fonts
   - Spacing tokens for uniform layouts

4. **Enable Auto Layout**
   - Use Auto Layout for responsive behavior
   - Set up proper constraints for mobile/desktop

### Component Mapping Strategy

Map Figma components to your existing React components:

| Figma Component | React Component | File Path |
|----------------|----------------|-----------|
| Hero Section | HeroSection.tsx | `frontend/src/components/landing/HeroSection.tsx` |
| Navigation | NavigationHeader.tsx | `frontend/src/components/landing/NavigationHeader.tsx` |
| Features | FeaturesSection.tsx | `frontend/src/components/landing/FeaturesSection.tsx` |
| Benefits | BenefitsSection.tsx | `frontend/src/components/landing/BenefitsSection.tsx` |
| Testimonials | TestimonialsSection.tsx | `frontend/src/components/landing/TestimonialsSection.tsx` |
| CTA | CTASection.tsx | `frontend/src/components/landing/CTASection.tsx` |
| Footer | FooterSection.tsx | `frontend/src/components/landing/FooterSection.tsx` |

## 🔧 Troubleshooting

### Connection Issues

**Problem**: "No MCP tools available"
**Solution**: 
1. Ensure Figma desktop app is open
2. Verify MCP server is enabled in Figma preferences
3. Restart VS Code
4. Check server URL: `http://127.0.0.1:3845/sse`

**Problem**: "Connection refused"
**Solution**:
1. Check if Figma desktop app is running
2. Verify Dev Mode is enabled
3. Try restarting Figma desktop app

### Code Generation Issues

**Problem**: Generated code doesn't match design
**Solution**:
1. Use smaller, more specific selections
2. Ensure Figma components are properly structured
3. Use semantic layer names
4. Enable Code Connect for component mapping

**Problem**: Slow or incomplete responses
**Solution**:
1. Reduce selection size
2. Focus on individual components
3. Break down large frames into smaller sections

## 📋 Quick Commands Reference

### Basic Figma MCP Commands

```bash
# Generate React code for current selection
"Generate React TypeScript code for my Figma selection using Tailwind CSS"

# Extract design tokens
"Get color and spacing variables from my Figma selection"

# Update existing component
"Update HeroSection.tsx to match this Figma design"

# Create new component
"Create a new landing page section component from this Figma selection"

# Analyze layout
"Analyze the layout and spacing of this Figma selection for Tailwind CSS"
```

### Landing Page Specific Commands

```bash
# Generate hero section
"Generate a hero section component from this Figma selection for our landing page"

# Create feature cards
"Generate feature card components from this Figma selection using our design system"

# Extract navigation
"Generate navigation header component from this Figma selection"

# Create testimonials
"Generate testimonials section from this Figma selection with proper TypeScript types"
```

## 🎯 Next Steps

1. **Open your landing page design in Figma desktop app**
2. **Enable Dev Mode MCP Server** in Figma preferences
3. **Restart VS Code** to load the MCP configuration
4. **Select a landing page section** in Figma
5. **Use the prompts above** to generate or update components
6. **Test the generated code** in your React application

## 📞 Support

- **Figma MCP Documentation**: https://help.figma.com/hc/en-us/articles/32132100833559
- **Augment MCP Setup**: https://docs.augmentcode.com/setup-augment/mcp
- **Model Context Protocol**: https://modelcontextprotocol.io/

---

**Status**: ✅ Configured and ready for landing page development
**Last Updated**: 2025-01-27 